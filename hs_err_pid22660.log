#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 33296 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:389), pid=22660, tid=0x0000000000016e10
#
# JRE version: Java(TM) SE Runtime Environment (8.0_291-b10) (build 1.8.0_291-b10)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.291-b10 mixed mode windows-amd64 compressed oops)
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#

---------------  T H R E A D  ---------------

Current thread (0x000002445ab32800):  JavaThread "main" [_thread_in_native, id=93712, stack(0x0000007fe2700000,0x0000007fe2800000)]

Stack: [0x0000007fe2700000,0x0000007fe2800000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x32ea19]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.util.concurrent.CompletableFuture.<clinit>()V+11
v  ~StubRoutines::call_stub
j  org.springframework.data.redis.connection.lettuce.LettucePoolingConnectionProvider.destroy()V+193
j  org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory$ExceptionTranslatingConnectionProvider.destroy()V+17
j  org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.dispose(Lorg/springframework/data/redis/connection/lettuce/LettuceConnectionProvider;)V+11
j  org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory.destroy()V+39
j  org.springframework.beans.factory.support.DisposableBeanAdapter.destroy()V+139
j  org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(Ljava/lang/String;Lorg/springframework/beans/factory/DisposableBean;)V+133
j  org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(Ljava/lang/String;)V+41
j  org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(Ljava/lang/String;)V+2
j  org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons()V+105
j  org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons()V+1
j  org.springframework.context.support.AbstractApplicationContext.destroyBeans()V+4
j  org.springframework.context.support.AbstractApplicationContext.refresh()V+150
j  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh()V+1
j  org.springframework.boot.SpringApplication.refresh(Lorg/springframework/context/ConfigurableApplicationContext;)V+1
j  org.springframework.boot.SpringApplication.refreshContext(Lorg/springframework/context/ConfigurableApplicationContext;)V+16
j  org.springframework.boot.SpringApplication.run([Ljava/lang/String;)Lorg/springframework/context/ConfigurableApplicationContext;+107
j  org.springframework.boot.builder.SpringApplicationBuilder.run([Ljava/lang/String;)Lorg/springframework/context/ConfigurableApplicationContext;+45
j  com.allcore.core.launch.AllcoreApplication.run(Ljava/lang/String;Ljava/lang/Class;[Ljava/lang/String;)Lorg/springframework/context/ConfigurableApplicationContext;+9
j  com.allcore.ExternalApplication.main([Ljava/lang/String;)V+5
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x000002447eda2000 JavaThread "BufferPoolPruner-1-thread-1" daemon [_thread_blocked, id=23584, stack(0x0000007fe5c00000,0x0000007fe5d00000)]
  0x000002447eda1800 JavaThread "JMX server connection timeout 102" daemon [_thread_blocked, id=107392, stack(0x0000007fe7500000,0x0000007fe7600000)]
  0x000002447eda6800 JavaThread "sentinel-time-tick-thread" daemon [_thread_blocked, id=79864, stack(0x0000007fe7400000,0x0000007fe7500000)]
  0x000002440ad17000 JavaThread "nacos-grpc-client-executor-127.0.0.1-13" daemon [_thread_blocked, id=118732, stack(0x0000007fe7300000,0x0000007fe7400000)]
  0x000002440ad10800 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=114924, stack(0x0000007fe7200000,0x0000007fe7300000)]
  0x000002440ad0f000 JavaThread "lettuce-timer-3-1" daemon [_thread_blocked, id=94248, stack(0x0000007fe7100000,0x0000007fe7200000)]
  0x0000024409d2e000 JavaThread "Thread-38" daemon [_thread_in_native, id=101572, stack(0x0000007fe7000000,0x0000007fe7100000)]
  0x0000024409d22000 JavaThread "Druid-ConnectionPool-Destroy-362427044" daemon [_thread_blocked, id=23944, stack(0x0000007fe6f00000,0x0000007fe7000000)]
  0x0000024409d29000 JavaThread "Druid-ConnectionPool-Create-362427044" daemon [_thread_blocked, id=94564, stack(0x0000007fe6e00000,0x0000007fe6f00000)]
  0x0000024409d21800 JavaThread "Druid-ConnectionPool-Destroy-1352532563" daemon [_thread_blocked, id=96888, stack(0x0000007fe6d00000,0x0000007fe6e00000)]
  0x0000024409d28000 JavaThread "Druid-ConnectionPool-Create-1352532563" daemon [_thread_blocked, id=114344, stack(0x0000007fe6c00000,0x0000007fe6d00000)]
  0x0000024409d27800 JavaThread "mysql-cj-abandoned-connection-cleanup" daemon [_thread_blocked, id=116832, stack(0x0000007fe5600000,0x0000007fe5700000)]
  0x0000024409d23000 JavaThread "sentinel-command-center-executor-thread-1" daemon [_thread_in_native, id=117084, stack(0x0000007fe6b00000,0x0000007fe6c00000)]
  0x0000024408747800 JavaThread "sentinel-heartbeat-send-task-thread-1" daemon [_thread_in_native, id=117948, stack(0x0000007fe6a00000,0x0000007fe6b00000)]
  0x0000024403b4d800 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=109892, stack(0x0000007fe6900000,0x0000007fe6a00000)]
  0x0000024402703800 JavaThread "grpc-nio-worker-ELG-1-2" daemon [_thread_in_native, id=111648, stack(0x0000007fe4d00000,0x0000007fe4e00000)]
  0x0000024402709800 JavaThread "grpc-default-executor-0" daemon [_thread_blocked, id=114376, stack(0x0000007fe5b00000,0x0000007fe5c00000)]
  0x0000024402702000 JavaThread "grpc-nio-worker-ELG-1-1" daemon [_thread_in_native, id=78116, stack(0x0000007fe5a00000,0x0000007fe5b00000)]
  0x000002447e7c1000 JavaThread "com.alibaba.nacos.client.remote.worker" daemon [_thread_blocked, id=114692, stack(0x0000007fe5900000,0x0000007fe5a00000)]
  0x000002447e7c3800 JavaThread "com.alibaba.nacos.client.remote.worker" daemon [_thread_blocked, id=85776, stack(0x0000007fe5800000,0x0000007fe5900000)]
  0x000002447e7c5800 JavaThread "nacos.publisher-com.alibaba.nacos.common.notify.SlowEvent" daemon [_thread_blocked, id=105972, stack(0x0000007fe5700000,0x0000007fe5800000)]
  0x000002447e7be800 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=107088, stack(0x0000007fe5500000,0x0000007fe5600000)]
  0x000002447e7bb000 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=94416, stack(0x0000007fe5400000,0x0000007fe5500000)]
  0x000002447e7b9800 JavaThread "com.alibaba.nacos.client.Worker" daemon [_thread_blocked, id=116768, stack(0x0000007fe5300000,0x0000007fe5400000)]
  0x000002447e7bd000 JavaThread "com.alibaba.nacos.client.auth.ram.identify.watcher.0" daemon [_thread_blocked, id=116944, stack(0x0000007fe5200000,0x0000007fe5300000)]
  0x000002447e7b7000 JavaThread "Keep-Alive-Timer" daemon [_thread_blocked, id=102260, stack(0x0000007fe5100000,0x0000007fe5200000)]
  0x000002447ebcd000 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=97148, stack(0x0000007fe4f00000,0x0000007fe5000000)]
  0x000002447ebbe800 JavaThread "RMI TCP Connection(1)-10.10.11.8" daemon [_thread_in_native, id=61308, stack(0x0000007fe4e00000,0x0000007fe4f00000)]
  0x000002447ebc1000 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=115116, stack(0x0000007fe4b00000,0x0000007fe4c00000)]
  0x000002447e28a000 JavaThread "Service Thread" daemon [_thread_blocked, id=112304, stack(0x0000007fe4a00000,0x0000007fe4b00000)]
  0x000002447e289800 JavaThread "C1 CompilerThread11" daemon [_thread_blocked, id=116868, stack(0x0000007fe4900000,0x0000007fe4a00000)]
  0x000002447e28f800 JavaThread "C1 CompilerThread10" daemon [_thread_blocked, id=106972, stack(0x0000007fe4800000,0x0000007fe4900000)]
  0x000002447e288800 JavaThread "C1 CompilerThread9" daemon [_thread_blocked, id=117544, stack(0x0000007fe4700000,0x0000007fe4800000)]
  0x000002447e28e000 JavaThread "C1 CompilerThread8" daemon [_thread_blocked, id=6468, stack(0x0000007fe4600000,0x0000007fe4700000)]
  0x000002447e28e800 JavaThread "C2 CompilerThread7" daemon [_thread_blocked, id=107972, stack(0x0000007fe4500000,0x0000007fe4600000)]
  0x000002447e28d000 JavaThread "C2 CompilerThread6" daemon [_thread_blocked, id=19384, stack(0x0000007fe4400000,0x0000007fe4500000)]
  0x000002447e28c800 JavaThread "C2 CompilerThread5" daemon [_thread_blocked, id=102240, stack(0x0000007fe4300000,0x0000007fe4400000)]
  0x000002447e28b800 JavaThread "C2 CompilerThread4" daemon [_thread_blocked, id=117276, stack(0x0000007fe4200000,0x0000007fe4300000)]
  0x000002447ec23000 JavaThread "C2 CompilerThread3" daemon [_thread_blocked, id=117100, stack(0x0000007fe4100000,0x0000007fe4200000)]
  0x000002447ec09800 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=117992, stack(0x0000007fe4000000,0x0000007fe4100000)]
  0x000002447ec08800 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=97524, stack(0x0000007fe3f00000,0x0000007fe4000000)]
  0x000002447ebf7000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=103336, stack(0x0000007fe3e00000,0x0000007fe3f00000)]
  0x000002447e28b000 JavaThread "IntelliJ Suspend Helper" daemon [_thread_blocked, id=109936, stack(0x0000007fe3d00000,0x0000007fe3e00000)]
  0x000002447c207800 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=114112, stack(0x0000007fe3c00000,0x0000007fe3d00000)]
  0x000002447c1f5000 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=98168, stack(0x0000007fe3b00000,0x0000007fe3c00000)]
  0x000002447c1f2000 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=22384, stack(0x0000007fe3a00000,0x0000007fe3b00000)]
  0x000002447c174000 JavaThread "Attach Listener" daemon [_thread_blocked, id=106608, stack(0x0000007fe3900000,0x0000007fe3a00000)]
  0x000002447c1c7800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=111964, stack(0x0000007fe3800000,0x0000007fe3900000)]
  0x000002447ac7e000 JavaThread "Finalizer" daemon [_thread_blocked, id=117724, stack(0x0000007fe3700000,0x0000007fe3800000)]
  0x000002447c14b800 JavaThread "Reference Handler" daemon [_thread_blocked, id=102868, stack(0x0000007fe3600000,0x0000007fe3700000)]
=>0x000002445ab32800 JavaThread "main" [_thread_in_native, id=93712, stack(0x0000007fe2700000,0x0000007fe2800000)]

Other Threads:
  0x000002447c126800 VMThread [stack: 0x0000007fe3500000,0x0000007fe3600000] [id=116076]
  0x000002447f16d000 WatcherThread [stack: 0x0000007fe4c00000,0x0000007fe4d00000] [id=101696]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

OutOfMemory and StackOverflow Exception counts:
OutOfMemoryError java_heap_errors=2

heap address: 0x0000000604e00000, size: 7090 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 373760K, used 18653K [0x000000072c500000, 0x0000000745400000, 0x00000007c0000000)
  eden space 346112K, 5% used [0x000000072c500000,0x000000072d7376b8,0x0000000741700000)
  from space 27648K, 0% used [0x0000000741700000,0x0000000741700000,0x0000000743200000)
  to   space 27136K, 0% used [0x0000000743980000,0x0000000743980000,0x0000000745400000)
 ParOldGen       total 449536K, used 48707K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 10% used [0x0000000604e00000,0x0000000607d90dc0,0x0000000620500000)
 Metaspace       used 73325K, capacity 77195K, committed 77440K, reserved 1120256K
  class space    used 9876K, capacity 10600K, committed 10624K, reserved 1048576K

Card table byte_map: [0x0000024469f70000,0x000002446ad50000] byte_map_base: 0x0000024466f49000

Marking Bits: (ParMarkBitMap*) 0x000000005e207fe0
 Begin Bits: [0x000002446b690000, 0x0000024472558000)
 End Bits:   [0x0000024472558000, 0x0000024479420000)

Polling page: 0x0000024458830000

CodeCache: size=245760Kb used=17473Kb max_used=17473Kb free=228286Kb
 bounds [0x000002445abb0000, 0x000002445bcd0000, 0x0000024469bb0000]
 total_blobs=7802 nmethods=7171 adapters=546
 compilation: disabled (not enough contiguous free space left)

Compilation events (10 events):
Event: 18.736 Thread 0x000002447e288800 7257       1       org.springframework.core.ResolvableType::isWildcardWithoutBounds (58 bytes)
Event: 18.736 Thread 0x000002447e288800 nmethod 7257 0x000002445bcbf3d0 code [0x000002445bcbf560, 0x000002445bcbf800]
Event: 18.736 Thread 0x000002447e28f800 7260       1       org.springframework.core.ResolvableType::resolveGenerics (41 bytes)
Event: 18.736 Thread 0x000002447e289800 7258       1       org.springframework.core.GenericTypeResolver::resolveTypeArguments (32 bytes)
Event: 18.736 Thread 0x000002447e28e000 7259       1       org.springframework.core.ResolvableType::isEntirelyUnresolvable (60 bytes)
Event: 18.736 Thread 0x000002447e288800 7261       1       org.springframework.data.util.Streamable::stream (11 bytes)
Event: 18.736 Thread 0x000002447e28e000 nmethod 7259 0x000002445bcbfa90 code [0x000002445bcbfc20, 0x000002445bcbfe50]
Event: 18.736 Thread 0x000002447e28f800 

Event: 18.736 Thread 0x000002447e289800 nmethod 7258 0x000002445bcc00d0 code [0x000002445bcc0260, 0x000002445bcc04d0]
Event: 18.736 Thread 0x000002447e288800 nmethod 7261 0x000002445bcc0790 code [0x000002445bcc0920, 0x000002445bcc0c00]

GC Heap History (10 events):
Event: 18.109 GC heap before
{Heap before GC invocations=18 (full 5):
 PSYoungGen      total 373760K, used 448K [0x000000072c500000, 0x0000000745880000, 0x00000007c0000000)
  eden space 345600K, 0% used [0x000000072c500000,0x000000072c500000,0x0000000741680000)
  from space 28160K, 1% used [0x0000000741680000,0x00000007416f0000,0x0000000743200000)
  to   space 25088K, 0% used [0x0000000743200000,0x0000000743200000,0x0000000744a80000)
 ParOldGen       total 449536K, used 46043K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 10% used [0x0000000604e00000,0x0000000607af6fa8,0x0000000620500000)
 Metaspace       used 69645K, capacity 72982K, committed 73216K, reserved 1114112K
  class space    used 9322K, capacity 9959K, committed 9984K, reserved 1048576K
Event: 18.176 GC heap after
Heap after GC invocations=18 (full 5):
 PSYoungGen      total 373760K, used 0K [0x000000072c500000, 0x0000000745880000, 0x00000007c0000000)
  eden space 345600K, 0% used [0x000000072c500000,0x000000072c500000,0x0000000741680000)
  from space 28160K, 0% used [0x0000000741680000,0x0000000741680000,0x0000000743200000)
  to   space 25088K, 0% used [0x0000000743200000,0x0000000743200000,0x0000000744a80000)
 ParOldGen       total 449536K, used 42792K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 9% used [0x0000000604e00000,0x00000006077ca330,0x0000000620500000)
 Metaspace       used 69645K, capacity 72982K, committed 73216K, reserved 1114112K
  class space    used 9322K, capacity 9959K, committed 9984K, reserved 1048576K
}
Event: 18.934 GC heap before
{Heap before GC invocations=19 (full 5):
 PSYoungGen      total 373760K, used 110070K [0x000000072c500000, 0x0000000745880000, 0x00000007c0000000)
  eden space 345600K, 31% used [0x000000072c500000,0x000000073307dba8,0x0000000741680000)
  from space 28160K, 0% used [0x0000000741680000,0x0000000741680000,0x0000000743200000)
  to   space 25088K, 0% used [0x0000000743200000,0x0000000743200000,0x0000000744a80000)
 ParOldGen       total 449536K, used 42792K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 9% used [0x0000000604e00000,0x00000006077ca330,0x0000000620500000)
 Metaspace       used 72194K, capacity 75934K, committed 76032K, reserved 1120256K
  class space    used 9719K, capacity 10425K, committed 10496K, reserved 1048576K
Event: 18.938 GC heap after
Heap after GC invocations=19 (full 5):
 PSYoungGen      total 371200K, used 4431K [0x000000072c500000, 0x0000000745680000, 0x00000007c0000000)
  eden space 346112K, 0% used [0x000000072c500000,0x000000072c500000,0x0000000741700000)
  from space 25088K, 17% used [0x0000000743200000,0x0000000743653d30,0x0000000744a80000)
  to   space 27648K, 0% used [0x0000000741700000,0x0000000741700000,0x0000000743200000)
 ParOldGen       total 449536K, used 42800K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 9% used [0x0000000604e00000,0x00000006077cc330,0x0000000620500000)
 Metaspace       used 72194K, capacity 75934K, committed 76032K, reserved 1120256K
  class space    used 9719K, capacity 10425K, committed 10496K, reserved 1048576K
}
Event: 18.938 GC heap before
{Heap before GC invocations=20 (full 6):
 PSYoungGen      total 371200K, used 4431K [0x000000072c500000, 0x0000000745680000, 0x00000007c0000000)
  eden space 346112K, 0% used [0x000000072c500000,0x000000072c500000,0x0000000741700000)
  from space 25088K, 17% used [0x0000000743200000,0x0000000743653d30,0x0000000744a80000)
  to   space 27648K, 0% used [0x0000000741700000,0x0000000741700000,0x0000000743200000)
 ParOldGen       total 449536K, used 42800K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 9% used [0x0000000604e00000,0x00000006077cc330,0x0000000620500000)
 Metaspace       used 72194K, capacity 75934K, committed 76032K, reserved 1120256K
  class space    used 9719K, capacity 10425K, committed 10496K, reserved 1048576K
Event: 19.221 GC heap after
Heap after GC invocations=20 (full 6):
 PSYoungGen      total 371200K, used 0K [0x000000072c500000, 0x0000000745680000, 0x00000007c0000000)
  eden space 346112K, 0% used [0x000000072c500000,0x000000072c500000,0x0000000741700000)
  from space 25088K, 0% used [0x0000000743200000,0x0000000743200000,0x0000000744a80000)
  to   space 27648K, 0% used [0x0000000741700000,0x0000000741700000,0x0000000743200000)
 ParOldGen       total 449536K, used 46265K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 10% used [0x0000000604e00000,0x0000000607b2e538,0x0000000620500000)
 Metaspace       used 72194K, capacity 75934K, committed 76032K, reserved 1118208K
  class space    used 9719K, capacity 10425K, committed 10496K, reserved 1048576K
}
Event: 19.724 GC heap before
{Heap before GC invocations=21 (full 6):
 PSYoungGen      total 371200K, used 93655K [0x000000072c500000, 0x0000000745680000, 0x00000007c0000000)
  eden space 346112K, 27% used [0x000000072c500000,0x0000000732075f90,0x0000000741700000)
  from space 25088K, 0% used [0x0000000743200000,0x0000000743200000,0x0000000744a80000)
  to   space 27648K, 0% used [0x0000000741700000,0x0000000741700000,0x0000000743200000)
 ParOldGen       total 449536K, used 46265K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 10% used [0x0000000604e00000,0x0000000607b2e538,0x0000000620500000)
 Metaspace       used 72972K, capacity 76784K, committed 76928K, reserved 1122304K
  class space    used 9826K, capacity 10528K, committed 10624K, reserved 1048576K
Event: 19.728 GC heap after
Heap after GC invocations=21 (full 6):
 PSYoungGen      total 373760K, used 5061K [0x000000072c500000, 0x0000000745400000, 0x00000007c0000000)
  eden space 346112K, 0% used [0x000000072c500000,0x000000072c500000,0x0000000741700000)
  from space 27648K, 18% used [0x0000000741700000,0x0000000741bf15b0,0x0000000743200000)
  to   space 27136K, 0% used [0x0000000743980000,0x0000000743980000,0x0000000745400000)
 ParOldGen       total 449536K, used 46265K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 10% used [0x0000000604e00000,0x0000000607b2e538,0x0000000620500000)
 Metaspace       used 72972K, capacity 76784K, committed 76928K, reserved 1122304K
  class space    used 9826K, capacity 10528K, committed 10624K, reserved 1048576K
}
Event: 19.728 GC heap before
{Heap before GC invocations=22 (full 7):
 PSYoungGen      total 373760K, used 5061K [0x000000072c500000, 0x0000000745400000, 0x00000007c0000000)
  eden space 346112K, 0% used [0x000000072c500000,0x000000072c500000,0x0000000741700000)
  from space 27648K, 18% used [0x0000000741700000,0x0000000741bf15b0,0x0000000743200000)
  to   space 27136K, 0% used [0x0000000743980000,0x0000000743980000,0x0000000745400000)
 ParOldGen       total 449536K, used 46265K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 10% used [0x0000000604e00000,0x0000000607b2e538,0x0000000620500000)
 Metaspace       used 72972K, capacity 76784K, committed 76928K, reserved 1122304K
  class space    used 9826K, capacity 10528K, committed 10624K, reserved 1048576K
Event: 19.835 GC heap after
Heap after GC invocations=22 (full 7):
 PSYoungGen      total 373760K, used 0K [0x000000072c500000, 0x0000000745400000, 0x00000007c0000000)
  eden space 346112K, 0% used [0x000000072c500000,0x000000072c500000,0x0000000741700000)
  from space 27648K, 0% used [0x0000000741700000,0x0000000741700000,0x0000000743200000)
  to   space 27136K, 0% used [0x0000000743980000,0x0000000743980000,0x0000000745400000)
 ParOldGen       total 449536K, used 48707K [0x0000000604e00000, 0x0000000620500000, 0x000000072c500000)
  object space 449536K, 10% used [0x0000000604e00000,0x0000000607d90dc0,0x0000000620500000)
 Metaspace       used 72972K, capacity 76784K, committed 76928K, reserved 1120256K
  class space    used 9826K, capacity 10528K, committed 10624K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Classes redefined (1 events):
Event: 1.211 Thread 0x000002447c126800 redefined class name=java.lang.Throwable, count=1

Internal exceptions (10 events):
Event: 19.675 Thread 0x000002445ab32800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x000000073170e330) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u291\1294\hotspot\src\share\vm\runtime\sharedRuntime.cpp, line 609]
Event: 19.675 Thread 0x000002445ab32800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x0000000731712158) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u291\1294\hotspot\src\share\vm\runtime\sharedRuntime.cpp, line 609]
Event: 19.675 Thread 0x000002445ab32800 Exception <a 'java/lang/ArrayIndexOutOfBoundsException'> (0x00000007317180b0) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u291\1294\hotspot\src\share\vm\runtime\sharedRuntime.cpp, line 609]
Event: 19.723 Thread 0x000002445ab32800 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.lambda$typeHierarchy$2(Ljava/lang/Class;Ljava/lang/Class;)Z> (0x0000000731d42060) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u291\1294\hotspot\src\share\vm\interpreter\li
Event: 19.723 Thread 0x000002445ab32800 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.lambda$negate$1(Ljava/lang/Class;Ljava/lang/Class;)Z> (0x0000000731d45198) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u291\1294\hotspot\src\share\vm\interpreter\linkResol
Event: 19.724 Thread 0x000002445ab32800 Exception <a 'java/lang/NoSuchMethodError': java.lang.Object.lambda$and$0(Lorg/springframework/data/projection/EntityProjectionIntrospector$ProjectionPredicate;Ljava/lang/Class;Ljava/lang/Class;)Z> (0x0000000731d4e040) thrown at [C:\jenkins\workspace\8-2-
Event: 19.873 Thread 0x000002445ab32800 Exception <a 'java/lang/OutOfMemoryError'> (0x000000072d0a3ce0) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u291\1294\hotspot\src\share\vm\prims\jni.cpp, line 737]
Event: 19.873 Thread 0x000002445ab32800 Exception <a 'java/lang/OutOfMemoryError'> (0x000000072d0a3ce0) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u291\1294\hotspot\src\share\vm\prims\jvm.cpp, line 1527]
Event: 19.880 Thread 0x000002447eda9800 Exception <a 'java/net/SocketException': Socket Closed> (0x000000072d5b6dc0) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u291\1294\hotspot\src\share\vm\prims\jni.cpp, line 737]
Event: 19.880 Thread 0x000002447eda5000 Exception <a 'java/lang/InterruptedException': sleep interrupted> (0x000000072d677268) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u291\1294\hotspot\src\share\vm\prims\jvm.cpp, line 3357]

Events (10 events):
Event: 19.887 Thread 0x000002447eda9800 DEOPT PACKING pc=0x000002445b3a3614 sp=0x0000007fe5dfb850
Event: 19.887 loading class java/util/concurrent/CompletableFuture$AltResult
Event: 19.887 Thread 0x000002447eda9800 DEOPT UNPACKING pc=0x000002445abf7498 sp=0x0000007fe5dfb670 mode 1
Event: 19.887 Thread 0x000002447eda9800 DEOPT PACKING pc=0x000002445b3a3554 sp=0x0000007fe5dfb9e0
Event: 19.887 Thread 0x000002447eda9800 DEOPT UNPACKING pc=0x000002445abf7498 sp=0x0000007fe5dfb800 mode 1
Event: 19.887 loading class java/util/concurrent/CompletableFuture$AltResult done
Event: 19.887 Executing VM operation: RevokeBias
Event: 19.887 Executing VM operation: RevokeBias done
Event: 19.887 Thread 0x000002447eda9800 Thread exited: 0x000002447eda9800
Event: 19.887 loading class java/util/concurrent/ForkJoinPool


Dynamic libraries:
0x00007ff622150000 - 0x00007ff622197000 	C:\Program Files\Java\jdk1.8.0_291\bin\java.exe
0x00007ffa60f70000 - 0x00007ffa61187000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa601f0000 - 0x00007ffa602b4000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa5e460000 - 0x00007ffa5e833000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa5ed80000 - 0x00007ffa5ee31000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa5f550000 - 0x00007ffa5f5f7000 	C:\Windows\System32\msvcrt.dll
0x00007ffa60be0000 - 0x00007ffa60c87000 	C:\Windows\System32\sechost.dll
0x00007ffa5ebd0000 - 0x00007ffa5ebf8000 	C:\Windows\System32\bcrypt.dll
0x00007ffa603e0000 - 0x00007ffa604f4000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa60c90000 - 0x00007ffa60e41000 	C:\Windows\System32\USER32.dll
0x00007ffa5ea30000 - 0x00007ffa5ea56000 	C:\Windows\System32\win32u.dll
0x00007ffa60600000 - 0x00007ffa60629000 	C:\Windows\System32\GDI32.dll
0x00007ffa5e840000 - 0x00007ffa5e962000 	C:\Windows\System32\gdi32full.dll
0x00007ffa5e060000 - 0x00007ffa5e0fa000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa5e340000 - 0x00007ffa5e451000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa4fa50000 - 0x00007ffa4fceb000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5262_none_2712bde373830908\COMCTL32.dll
0x00007ffa602c0000 - 0x00007ffa602f1000 	C:\Windows\System32\IMM32.DLL
0x00007ffa3c4a0000 - 0x00007ffa3c4b5000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\vcruntime140.dll
0x00007ff9818a0000 - 0x00007ff98193b000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\msvcp140.dll
0x000000005da20000 - 0x000000005e280000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\server\jvm.dll
0x00007ffa60070000 - 0x00007ffa60078000 	C:\Windows\System32\PSAPI.DLL
0x00007ffa28730000 - 0x00007ffa28739000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffa57d20000 - 0x00007ffa57d2a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa57e10000 - 0x00007ffa57e44000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa5ec70000 - 0x00007ffa5ece1000 	C:\Windows\System32\WS2_32.dll
0x00007ffa5d060000 - 0x00007ffa5d078000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa278e0000 - 0x00007ffa278f0000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\verify.dll
0x00007ffa13960000 - 0x00007ffa1398b000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\java.dll
0x00007ffa2d2e0000 - 0x00007ffa2d316000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\jdwp.dll
0x00007ffa41cd0000 - 0x00007ffa41cd9000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\npt.dll
0x00007ff9eec00000 - 0x00007ff9eec30000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\instrument.dll
0x00007ff9e7330000 - 0x00007ff9e7528000 	C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder4\libasyncProfiler.dll
0x00007ffa13940000 - 0x00007ffa13958000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\zip.dll
0x00007ffa5f600000 - 0x00007ffa5fe9d000 	C:\Windows\System32\SHELL32.dll
0x00007ffa5e200000 - 0x00007ffa5e33f000 	C:\Windows\System32\wintypes.dll
0x00007ffa5ee40000 - 0x00007ffa5f1d3000 	C:\Windows\System32\combase.dll
0x00007ffa5bf50000 - 0x00007ffa5c86d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffa5f440000 - 0x00007ffa5f54b000 	C:\Windows\System32\SHCORE.dll
0x00007ffa60ec0000 - 0x00007ffa60f26000 	C:\Windows\System32\shlwapi.dll
0x00007ffa5df90000 - 0x00007ffa5dfbb000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffa330b0000 - 0x00007ffa330ba000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\dt_socket.dll
0x00007ffa5d4e0000 - 0x00007ffa5d54a000 	C:\Windows\system32\mswsock.dll
0x00007ffa13920000 - 0x00007ffa1393c000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\net.dll
0x00007ffa5cb40000 - 0x00007ffa5cc38000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffa5cad0000 - 0x00007ffa5cafd000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffa5ec60000 - 0x00007ffa5ec69000 	C:\Windows\System32\NSI.dll
0x0000000060630000 - 0x0000000060656000 	C:\Program Files\Bonjour\mdnsNSP.dll
0x00007ffa58ce0000 - 0x00007ffa58cea000 	C:\Windows\System32\rasadhlp.dll
0x00007ffa574d0000 - 0x00007ffa57553000 	C:\Windows\System32\fwpuclnt.dll
0x00007ffa26000000 - 0x00007ffa2600d000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\management.dll
0x00007ff9dd7a0000 - 0x00007ff9dd7b3000 	C:\Program Files\Java\jdk1.8.0_291\jre\bin\nio.dll
0x00007ffa5d710000 - 0x00007ffa5d72b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffa5cfc0000 - 0x00007ffa5cff7000 	C:\Windows\system32\rsaenh.dll
0x00007ffa5d5a0000 - 0x00007ffa5d5c8000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffa5e180000 - 0x00007ffa5e1fb000 	C:\Windows\System32\bcryptprimitives.dll
0x00007ffa5d700000 - 0x00007ffa5d70c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffa57e50000 - 0x00007ffa57e69000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffa57df0000 - 0x00007ffa57e0f000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffa3e320000 - 0x00007ffa3e337000 	C:\Windows\system32\napinsp.dll
0x00007ffa3d8b0000 - 0x00007ffa3d8cb000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffa3e5c0000 - 0x00007ffa3e5d1000 	C:\Windows\System32\winrnr.dll
0x00007ffa58be0000 - 0x00007ffa58bff000 	C:\Windows\system32\wshbth.dll
0x00007ffa3da50000 - 0x00007ffa3da71000 	C:\Windows\system32\nlansp_c.dll

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:57209,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2025.1\captureAgent\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture9123645233111910064.props -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder4\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\ExternalApplication_2025_06_06_150356.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder2\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\ExternalApplication_2025_06_06_150356.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.allcore.ExternalApplication
java_class_path (initial): C:\Program Files\Java\jdk1.8.0_291\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\deploy.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\access-bridge-64.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\cldrdata.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\dnsns.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\jaccess.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\jfxrt.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\localedata.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\nashorn.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\sunec.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\sunjce_provider.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\sunmscapi.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\sunpkcs11.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\ext\zipfs.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\javaws.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\jfxswt.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\management-agent.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\plugin.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_291\jre\lib\rt.jar;D:\code\bl\allcore-common-base\allcore-external\target\classes;D:\maven\repository\com\allcore\allcore-core-boot\1.0.0.RELEASE\allcore-core-boot-1.0.0.RELEASE.jar;D:\maven\repository\com\allcore\allcore-core-context\1.0.0.RELEASE\allcore-core-context-1.0.0.RELEASE.jar;D:\maven\repository\com\allcore\allcore-core-db\1.0.0.RELEASE\allcore-core-db-1.0.0.RELEASE.jar;D:\maven\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.1\spring-boot-starter-jdbc-2.7.1.jar;D:\maven\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\maven\repository\org\springframework\spring-jdbc\5.3.21\spring-jdbc-5.3.21.jar;D:\maven\repository\com\ba
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_291
USERNAME=78669
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 116 Stepping 1, AuthenticAMD



---------------  S Y S T E M  ---------------

OS: Windows 10.0 , 64 bit Build 22621 (10.0.22621.5262)

CPU:total 16 (initial active 16) (16 cores per cpu, 1 threads per core) family 25 model 116 stepping 1, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, mmxext, 3dnowpref, lzcnt, sse4a, tsc, tscinvbit, tscinv, bmi1

Memory: 4k page, physical 29037428k(3511908k free), swap 55251952k(660k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (25.291-b10) for windows-amd64 JRE (1.8.0_291-b10), built on Apr  9 2021 00:02:00 by "java_re" with MS VC++ 15.9 (VS2017)

time: Fri Jun  6 15:04:16 2025
timezone: �й���׼ʱ��
elapsed time: 19.896589 seconds (0d 0h 0m 19s)

