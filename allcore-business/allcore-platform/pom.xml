<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>allcore-business</artifactId>
        <groupId>com.allcore</groupId>
        <version>PRODUCT.1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>allcore-platform</artifactId>
    <name>${project.artifactId}</name>
    <version>${allcore.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.19</version>
        </dependency>

        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.17</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.json/json -->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20180813</version>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-frontend-jaxws</artifactId>
            <version>3.1.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-transports-http</artifactId>
            <version>3.1.6</version>
        </dependency>

        <dependency>
            <groupId>jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>1.1</version>
        </dependency>


        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-platform-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-auto</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-codec</artifactId>
                    <groupId>commons-codec</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.10</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.6</version>
        </dependency>

        <!-- starter-actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-auth-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-system-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
       <!-- <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>basic-server-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>app-server-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>defect-server-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>app-server-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>main-server-api</artifactId>
            <version>${allcore.project.version}</version>
        </dependency> -->

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant</artifactId>
            <version>1.10.5</version>
        </dependency>

        <!--Job-->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-message-api</artifactId>
            <version>${allcore.project.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-file-api</artifactId>
            <version>${allcore.project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-isc-api</artifactId>
            <version>${allcore.project.version}</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <username>${docker.username}</username>
                    <password>${docker.password}</password>
                    <repository>${docker.registry.url}/${docker.namespace}/${project.artifactId}</repository>
                    <tag>${docker.version}</tag>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.42.0</version>
                <configuration>
                    <!--配置远程docker守护进程url-->
                    <dockerHost>${docker.host}</dockerHost>
                    <!--认证配置,用于私有registry认证-->
                    <authConfig>
                        <username>${docker.username}</username>
                        <password>${docker.password}</password>
                    </authConfig>
                    <!-- harbor镜像仓库地址-->
                    <pushRegistry>http://${docker.registry.url}</pushRegistry>
                    <images>
                        <image>
                            <!--推送到私有镜像仓库，镜像名需要添加仓库地址-->
                            <name>${docker.registry.url}/${docker.namespace}/${project.artifactId}:${docker.version}</name>
                            <!--定义镜像构建行为-->

                            <build>
                                <dockerFileDir>${project.basedir}</dockerFileDir>
                            </build>
                        </image>
                    </images>
                </configuration>

                <executions>
                    <execution>
                        <id>docker-exec</id>
                        <!-- 绑定mvn install阶段，当执行mvn install时 就会执行docker build 和docker push-->
                        <phase>install</phase>
                        <goals>
                            <goal>build</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>

    </build>
</project>
