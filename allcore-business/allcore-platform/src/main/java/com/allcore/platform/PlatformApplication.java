package com.allcore.platform;

import com.allcore.core.cloud.client.AllcoreCloudApplication;
import com.allcore.core.cloud.feign.EnableAllcoreFeign;
import com.allcore.core.launch.AllcoreApplication;
import com.allcore.core.launch.constant.AppConstant;
import com.allcore.platform.props.PlatformProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * platform-server启动器
 *
 * <AUTHOR>
 */
@AllcoreCloudApplication
@EnableAllcoreFeign
@EnableAsync
@EnableConfigurationProperties(PlatformProperties.class)
public class PlatformApplication {

	public static void main(String[] args) {
		AllcoreApplication.run(AppConstant.APPLICATION_PLATFORM_NAME, PlatformApplication.class, args);
	}

}

