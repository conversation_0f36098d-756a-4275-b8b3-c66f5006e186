package com.allcore.platform.launcher;

import com.allcore.core.auto.service.AutoService;
import com.allcore.core.launch.constant.AppConstant;
import com.allcore.core.launch.constant.NacosConstant;
import com.allcore.core.launch.service.LauncherService;
import com.allcore.core.launch.utils.PropsUtil;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.util.Properties;

/**
 * 启动参数拓展
 * <AUTHOR>
 * @date 2022/03/31 14:03
 **/
@AutoService(LauncherService.class)
public class PlatformLauncherServiceImpl implements LauncherService {


	@Override
	public void launcher(SpringApplicationBuilder builder, String appName, String profile, boolean isLocalDev) {
		Properties props = System.getProperties();
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.extension-configs[0].data-id", NacosConstant.dataId(AppConstant.APPLICATION_PLATFORM_NAME, profile));
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.extension-configs[0].group", NacosConstant.NACOS_CONFIG_GROUP);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.extension-configs[0].refresh", NacosConstant.NACOS_CONFIG_REFRESH);
		// 开启多数据源
		PropsUtil.setProperty(props, "spring.datasource.dynamic.enabled", "true");
		// 自定义命名空间
		// PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace", LauncherConstant.NACOS_NAMESPACE);
		// PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.namespace", LauncherConstant.NACOS_NAMESPACE);
		// 自定义分组
		// PropsUtil.setProperty(props, "spring.cloud.nacos.config.group", NacosConstant.NACOS_CONFIG_GROUP);
		// PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.group", NacosConstant.NACOS_CONFIG_GROUP);
	}

	@Override
	public int getOrder() {
		return 20;
	}
}
