package com.allcore.platform.controller;

import com.allcore.platform.service.MachineNestConsumeService;
import com.allcore.platform.vo.DroneLedgerVo;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import com.allcore.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 公共
 *
 * <AUTHOR>
 * @date 2022/08/02 10:37
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/drone")
@Api(value = "机巢", tags = "三维机巢接口")
public class DroneLedgerController {

	@Resource
	private MachineNestConsumeService machineNestConsumeService;


	@GetMapping("/findDroneLedger")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查询机巢信息", notes = "传入单位ID")
	public R<List<DroneLedgerVo>> findDroneLedger(String deptCode) {
		return machineNestConsumeService.selectInDeptCode(deptCode);
	}
}
