package com.allcore.platform.service;

import com.alibaba.fastjson.JSONObject;
import com.allcore.platform.vo.BoxVO;

import java.util.ArrayList;
import java.util.List;

/**
 * 黑盒代码
 */

public interface BlackBoxService {


	/**
	 *校验是否存在相同的数据
	 * @param param
	 * @return
	 */
	JSONObject querySmartBoxByParam(String param);



	/**
	 * 根据id进行删除
	 * @param  result
	 * @return
	 */
	boolean deleteBoxByParam(List<String> result);




}
