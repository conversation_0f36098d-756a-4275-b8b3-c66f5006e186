package com.allcore.platform.interceptor;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/09/26 16:34
 **/
@Slf4j
public class RequestBodyFilter implements Filter {


	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {

		// 初始化自定义HttpServletRequestWrapper
		BodyRequestWrapper bodyRequestWrapper = new BodyRequestWrapper((HttpServletRequest) servletRequest);
		// 替换原本的request
		filterChain.doFilter(bodyRequestWrapper, servletResponse);

	}


}

