package com.allcore.platform.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.enums.BizEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.CollectionUtil;
import com.allcore.core.tool.utils.IntegerPool;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.platform.entity.*;
import com.allcore.platform.mapper.DataMngJsonMapper;
import com.allcore.platform.mapper.DataMngJsonPicMapper;
import com.allcore.platform.mapper.DataMngJsonReportMapper;
import com.allcore.platform.mapper.DataMngLineReportMapper;
import com.allcore.platform.service.*;
import com.allcore.platform.vo.DataMngJsonPicVO;
import com.allcore.platform.vo.DataMngJsonReportVO;
import com.allcore.platform.vo.DataMngJsonVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.http.entity.ContentType;
import org.apache.tools.zip.ZipEntry;
import org.apache.tools.zip.ZipFile;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据处理json表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-08
 */
@Service
@AllArgsConstructor
public class DataMngJsonServiceImpl extends ZxhcServiceImpl<DataMngJsonMapper, DataMngJson> implements IDataMngJsonService {

	private final IDataMngJsonPicService dataMngJsonPicService;
	private final IDataMngJsonReportService dataMngJsonReportService;

	private final IDataMngJsonDangerService dataMngJsonDangerService;

	private final IDataMngJsonCrossService dataMngJsonCrossService;

	private final IOssClient ossClient;

	private final DataMngJsonPicMapper dataMngJsonPicMapper;

	private final DataMngJsonReportMapper dataMngJsonReportMapper;

	private final IDataMngLineReportService dataMngLineReportService;

	private final DataMngLineReportMapper dataMngLineReportMapper;


	@Override
	public IPage<DataMngJsonVO> selectDataMngJsonPage(IPage<DataMngJsonVO> page, DataMngJsonVO dataMngJson) {
		return page.setRecords(baseMapper.selectDataMngJsonPage(page, dataMngJson));
	}

    @Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean upload(MultipartFile file, String type) {
		if (!CommonConstant.ZIP_FORMAT.equalsIgnoreCase(file.getOriginalFilename()
			.substring(file.getOriginalFilename().lastIndexOf(StringPool.DOT) + IntegerPool.INT_1))) {
			throw new ServiceException("请选择ZIP文件");
		}
		switch (type){
			case "one":
				parseManyDataByZip(file);
				break;
			case "many":
				parseManyDataByZip(file);
				break;
			default:break;
		}
        return Boolean.TRUE;
    }

	@Override
	public List<DataMngJsonVO> getDataMngDangerInfosByParamList(String lineGuid, String towerGuidRange,String dangerType,String defectLevel) {

		List<DataMngJsonVO> rst = baseMapper.getDataMngDangerInfosByParamList( lineGuid,  towerGuidRange,dangerType, defectLevel);

		rst.forEach(e->{
			List<DataMngJsonPicVO> picVOList = dataMngJsonPicMapper.selectDataMngJsonPicList(e.getJsonGuid());
			//追加文件全路径
			List<String> picFileGuids = picVOList.stream().map(DataMngJsonPicVO::getPicGuid).collect(Collectors.toList());
			R<List<AllcoreFileVO>> picRst = ossClient.getFilesDetail(picFileGuids);
			if (picRst.isSuccess()) {
				picVOList.forEach(picVO->{
					picRst.getData().forEach(pic->{
						if(pic.getFileGuid().equals(picVO.getPicGuid())){
							picVO.setFilePath(pic.getStaticPath());
						}
					});
				});
			}
			e.setDataMngJsonPicVOList(picVOList);

			List<DataMngJsonReportVO> reportVOList = dataMngJsonReportMapper.selectDataMngJsonReportList(e.getJsonGuid());
			//追加文件全路径
			List<String> reportFileGuids = reportVOList.stream().map(DataMngJsonReportVO::getReportGuid).collect(Collectors.toList());
			R<List<AllcoreFileVO>> reportRst = ossClient.getFilesDetail(reportFileGuids);
			if (reportRst.isSuccess()) {
				reportVOList.forEach(reportVO->{
					reportRst.getData().forEach(report->{
						if(report.getFileGuid().equals(reportVO.getReportGuid())){
							reportVO.setFilePath(report.getStaticPath());
						}
					});
				});
			}
			e.setDataMngJsonReportVOList(reportVOList);
		});
		return rst;
	}

	@Override
	public List<DataMngJsonVO> getDataMngCrossInfosByParamList(String lineGuid, String towerGuidRange,String crossType) {

		List<DataMngJsonVO> rst = baseMapper.getDataMngCrossInfosByParamList( lineGuid,  towerGuidRange,crossType);

		rst.forEach(e->{
			List<DataMngJsonPicVO> picVOList = dataMngJsonPicMapper.selectDataMngJsonPicList(e.getJsonGuid());
			//追加文件全路径
			List<String> picFileGuids = picVOList.stream().map(DataMngJsonPicVO::getPicGuid).collect(Collectors.toList());
			R<List<AllcoreFileVO>> picRst = ossClient.getFilesDetail(picFileGuids);
			if (picRst.isSuccess()) {
				picVOList.forEach(picVO->{
					picRst.getData().forEach(pic->{
						if(pic.getFileGuid().equals(picVO.getPicGuid())){
							picVO.setFilePath(pic.getStaticPath());
						}
					});
				});
			}
			e.setDataMngJsonPicVOList(picVOList);

			List<DataMngJsonReportVO> reportVOList = dataMngJsonReportMapper.selectDataMngJsonReportList(e.getJsonGuid());
			//追加文件全路径
			List<String> reportFileGuids = reportVOList.stream().map(DataMngJsonReportVO::getReportGuid).collect(Collectors.toList());
			R<List<AllcoreFileVO>> reportRst = ossClient.getFilesDetail(reportFileGuids);
			if (reportRst.isSuccess()) {
				reportVOList.forEach(reportVO->{
					reportRst.getData().forEach(report->{
						if(report.getFileGuid().equals(reportVO.getReportGuid())){
							reportVO.setFilePath(report.getStaticPath());
						}
					});
				});
			}
			e.setDataMngJsonReportVOList(reportVOList);
		});
		return rst;
	}


	@Override
	public IPage<DataMngJsonVO> getDataMngDangerInfosByParamPage(String lineGuid, String towerGuidRange,String dangerType,String defectLevel,IPage<DataMngJsonVO> page) {

		List<DataMngJsonVO> rst = baseMapper.getDataMngDangerInfosByParamPage( lineGuid,  towerGuidRange,dangerType, defectLevel,page);

		rst.forEach(e->{
			List<DataMngJsonPicVO> picVOList = dataMngJsonPicMapper.selectDataMngJsonPicList(e.getJsonGuid());
			//追加文件全路径
			List<String> picFileGuids = picVOList.stream().map(DataMngJsonPicVO::getPicGuid).collect(Collectors.toList());
			R<List<AllcoreFileVO>> picRst = ossClient.getFilesDetail(picFileGuids);
			if (picRst.isSuccess()) {
				picVOList.forEach(picVO->{
					picRst.getData().forEach(pic->{
						if(pic.getFileGuid().equals(picVO.getPicGuid())){
							picVO.setFilePath(pic.getStaticPath());
						}
					});
				});
			}
			e.setDataMngJsonPicVOList(picVOList);

			List<DataMngJsonReportVO> reportVOList = dataMngJsonReportMapper.selectDataMngJsonReportList(e.getJsonGuid());
			//追加文件全路径
			List<String> reportFileGuids = reportVOList.stream().map(DataMngJsonReportVO::getReportGuid).collect(Collectors.toList());
			R<List<AllcoreFileVO>> reportRst = ossClient.getFilesDetail(reportFileGuids);
			if (reportRst.isSuccess()) {
				reportVOList.forEach(reportVO->{
					reportRst.getData().forEach(report->{
						if(report.getFileGuid().equals(reportVO.getReportGuid())){
							reportVO.setFilePath(report.getStaticPath());
						}
					});
				});
			}
			e.setDataMngJsonReportVOList(reportVOList);
		});
		return page.setRecords(rst);
	}

	@Override
	public IPage<DataMngJsonVO> getDataMngCrossInfosByParamPage(String lineGuid, String towerGuidRange,String crossType,IPage<DataMngJsonVO> page) {

		List<DataMngJsonVO> rst = baseMapper.getDataMngCrossInfosByParamPage( lineGuid,  towerGuidRange,crossType,page);

		rst.forEach(e->{
			List<DataMngJsonPicVO> picVOList = dataMngJsonPicMapper.selectDataMngJsonPicList(e.getJsonGuid());
			//追加文件全路径
			List<String> picFileGuids = picVOList.stream().map(DataMngJsonPicVO::getPicGuid).collect(Collectors.toList());
			R<List<AllcoreFileVO>> picRst = ossClient.getFilesDetail(picFileGuids);
			if (picRst.isSuccess()) {
				picVOList.forEach(picVO->{
					picRst.getData().forEach(pic->{
						if(pic.getFileGuid().equals(picVO.getPicGuid())){
							picVO.setFilePath(pic.getStaticPath());
						}
					});
				});
			}
			e.setDataMngJsonPicVOList(picVOList);

			List<DataMngJsonReportVO> reportVOList = dataMngJsonReportMapper.selectDataMngJsonReportList(e.getJsonGuid());
			//追加文件全路径
			List<String> reportFileGuids = reportVOList.stream().map(DataMngJsonReportVO::getReportGuid).collect(Collectors.toList());
			R<List<AllcoreFileVO>> reportRst = ossClient.getFilesDetail(reportFileGuids);
			if (reportRst.isSuccess()) {
				reportVOList.forEach(reportVO->{
					reportRst.getData().forEach(report->{
						if(report.getFileGuid().equals(reportVO.getReportGuid())){
							reportVO.setFilePath(report.getStaticPath());
						}
					});
				});
			}
			e.setDataMngJsonReportVOList(reportVOList);
		});
		return page.setRecords(rst);
	}


	@Transactional(rollbackFor = Exception.class)
	public void parseManyDataByZip(MultipartFile file) {

		ZipFile zf = null;
		Enumeration e;
		String property = System.getProperty("user.dir");
		String path = property + File.separator + "allcore-service" +
			File.separator + "platform-server" + File.separator + "src" + File.separator
			+ "main" + File.separator + "resources" + File.separator + "temp" +
			File.separator + System.currentTimeMillis() + ".zip";
		File file1 = new File(path);

		try {
			FileUtils.copyInputStreamToFile(file.getInputStream(), file1);
			zf = new ZipFile(path, "GBK"); //解决中文乱码问题
			e = zf.getEntries();
		} catch (IOException eee) {
			throw new RuntimeException(eee);
		}


		//zip包需要时大文件夹套一堆子问价夹格式 然后把大文件夹压缩成同名zip
		BufferedInputStream bi = null;

		String fileName;
		try {
			List<MultipartFile> multipartFilePngList = new ArrayList<>();
			List<MultipartFile> multipartFileReportList = new ArrayList<>();
			List<DataMngJsonDanger> dangers = new ArrayList<>();
			List<DataMngJsonCross> crosses = new ArrayList<>();
			String folderName = "";
			//保存json数据、归集 图片list 和 报告list

			//1.json存 DataMngJson
			//2.图片存 DataMngJsonPic
			//3.报告存 DataMngJsonReport
			DataMngJson dataMngJson = new DataMngJson();
			String jsonGuid = CommonUtil.generateUuid();
			dataMngJson.setJsonGuid(jsonGuid);

			while (e.hasMoreElements()) {
				ZipEntry ze2 = (ZipEntry) e.nextElement();
				fileName = ze2.getName();
				System.out.println(fileName);
				// 是文件夹
				if (ze2.isDirectory()) {
					//不为空 表示遍历到了第二个文件夹 先保存上一个文件夹数据,然后初始化两个文件list
					if(StringUtil.isNotBlank(folderName)){
						//保存 图片list
						addPicList(jsonGuid,multipartFilePngList);
						//保存 报告list
						addReportList(jsonGuid,multipartFileReportList);
						//保存 隐患点list
						dataMngJsonDangerService.saveBatch(dangers);
						//保存 交跨点list
						dataMngJsonCrossService.saveBatch(crosses);

						//初始化
						multipartFilePngList = new ArrayList<>();
						multipartFileReportList = new ArrayList<>();
						dangers = new ArrayList<>();
						crosses = new ArrayList<>();
						dataMngJson = new DataMngJson();
						jsonGuid = CommonUtil.generateUuid();
						dataMngJson.setJsonGuid(jsonGuid);
					}
					folderName = fileName;
				}
				else{
					// 存放文件的名字和文件
					if (CommonConstant.JSON_FORMAT.equalsIgnoreCase(fileName
						.substring(fileName.lastIndexOf(StringPool.DOT) + IntegerPool.INT_1))) {

						bi = new BufferedInputStream(zf.getInputStream(ze2));

						ByteArrayOutputStream byteArrayOutputStream=new ByteArrayOutputStream();
						byte[] fileByte=new byte[1024];
						int num=-1;
						while((num=bi.read(fileByte,0,fileByte.length))>-1){
							byteArrayOutputStream.write(fileByte,0,num);
						}
						byte[] byteS=byteArrayOutputStream.toByteArray();
						String fileContent=new String(byteS,"UTF-8");
						JSONObject jsonObject = JSONObject.parseObject(fileContent);
						//解析出线路id和档距段
						String lineGuid = jsonObject.getString("lineid");
						String lineName = jsonObject.getString("linename");
						//档距端范围
						String towerRange = jsonObject.getString("tower-range");
						//档距端guid范围
						String towerGuidRange = jsonObject.getString("tower-guid-range");
						//采集时间
						String cjTime = jsonObject.getString("cjTime");

						//解析隐患点数据
						JSONArray clearanceDangerPoints = jsonObject.getJSONArray("clearancedangerpoint");
						if(CollectionUtil.isNotEmpty(clearanceDangerPoints)){
							JSONObject oneObj = clearanceDangerPoints.getJSONObject(0);
							JSONArray childrenArr = oneObj.getJSONArray("children");
							if(CollectionUtil.isNotEmpty(childrenArr)){
								for(int i=0;i<childrenArr.size();i++){
									JSONObject children = (JSONObject) childrenArr.get(i);
									DataMngJsonDanger danger = new DataMngJsonDanger();
									danger.setJsonGuid(jsonGuid);
									danger.setDangerGuid(CommonUtil.generateUuid());
									danger.setDangerType(children.getString("type"));
									danger.setDefectLevel(children.getString("defectLevel"));
									danger.setDisToSmallTower(children.getDouble("dis-to-stower"));
									danger.setDisC(children.getDouble("disC"));
									danger.setDisH(children.getDouble("disH"));
									danger.setDisV(children.getDouble("disV"));
									JSONArray linePointArr = children.getJSONArray("line-point");
									danger.setLinePointLng(linePointArr.getDouble(0));
									danger.setLinePointLat(linePointArr.getDouble(1));
									danger.setLinePointAlt(linePointArr.getDouble(2));
									JSONArray locationArr = children.getJSONArray("location");
									danger.setLocationLng(locationArr.getDouble(0));
									danger.setLocationLat(locationArr.getDouble(1));
									danger.setLocationAlt(locationArr.getDouble(2));
									dangers.add(danger);
								}
							}
						}

						//解析隐患点数据
						JSONArray crossInfos = jsonObject.getJSONArray("crossinfo");
						if(CollectionUtil.isNotEmpty(crossInfos)){
							JSONObject oneObj = crossInfos.getJSONObject(0);
							JSONArray childrenArr = oneObj.getJSONArray("children");
							if(CollectionUtil.isNotEmpty(childrenArr)){
								for(int i=0;i<childrenArr.size();i++){
									JSONObject children = (JSONObject) childrenArr.get(i);
									DataMngJsonCross cross = new DataMngJsonCross();
									cross.setJsonGuid(jsonGuid);
									cross.setCrossGuid(CommonUtil.generateUuid());
									cross.setCrossType(children.getString("cross-type"));
									cross.setDisC(children.getDouble("disC"));
									cross.setDisH(children.getDouble("disH"));
									cross.setDisV(children.getDouble("disV"));
									JSONArray linePointArr = children.getJSONArray("line-point");
									cross.setLinePointLng(linePointArr.getDouble(0));
									cross.setLinePointLat(linePointArr.getDouble(1));
									cross.setLinePointAlt(linePointArr.getDouble(2));
									JSONArray locationArr = children.getJSONArray("location");
									cross.setLocationLng(locationArr.getDouble(0));
									cross.setLocationLat(locationArr.getDouble(1));
									cross.setLocationAlt(locationArr.getDouble(2));
									crosses.add(cross);
								}
							}
						}

						dataMngJson.setContent(fileContent);
						dataMngJson.setLineId(lineGuid);
						dataMngJson.setLineName(lineName);
						dataMngJson.setTowerRange(towerRange);
						dataMngJson.setTowerGuidRange(towerGuidRange);
						dataMngJson.setCjTime(cjTime);
						//如果有这个组合 先删除
						QueryWrapper<DataMngJson> queryWrapper = new QueryWrapper();
						queryWrapper.lambda().eq(DataMngJson::getLineName, lineName).eq(DataMngJson::getTowerRange,towerRange);
						List<DataMngJson> list = list(queryWrapper);
						if(CollectionUtil.isNotEmpty(list)){
							remove(new QueryWrapper<DataMngJson>().lambda().eq(DataMngJson::getJsonGuid,list.get(0).getJsonGuid()));
							dataMngJsonPicService.remove(new QueryWrapper<DataMngJsonPic>().lambda().eq(DataMngJsonPic::getJsonGuid,list.get(0).getJsonGuid()));
							dataMngJsonReportService.remove(new QueryWrapper<DataMngJsonReport>().lambda().eq(DataMngJsonReport::getJsonGuid,list.get(0).getJsonGuid()));
							dataMngJsonDangerService.remove(new QueryWrapper<DataMngJsonDanger>().lambda().eq(DataMngJsonDanger::getJsonGuid,list.get(0).getJsonGuid()));
							dataMngJsonCrossService.remove(new QueryWrapper<DataMngJsonCross>().lambda().eq(DataMngJsonCross::getJsonGuid,list.get(0).getJsonGuid()));
						}
						save(dataMngJson);
						byteArrayOutputStream.close();
					}
					if (CommonConstant.PNG_FORMAT.equalsIgnoreCase(fileName
						.substring(fileName.lastIndexOf(StringPool.DOT) + IntegerPool.INT_1))) {
						bi = new BufferedInputStream(zf.getInputStream(ze2));

						//每个文件的流
						byte[] bytes = new byte[(int)ze2.getSize()];
						bi.read(bytes,0,(int)ze2.getSize());
						InputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
						MultipartFile multipartFile = new MockMultipartFile(fileName,fileName, ContentType.IMAGE_PNG.toString(),byteArrayInputStream);
						multipartFilePngList.add(multipartFile);
						byteArrayInputStream.close();
					}
					if (CommonConstant.PDF_FORMAT.equalsIgnoreCase(fileName
						.substring(fileName.lastIndexOf(StringPool.DOT) + IntegerPool.INT_1))) {
						bi = new BufferedInputStream(zf.getInputStream(ze2));

						//每个文件的流
						byte[] bytes = new byte[(int)ze2.getSize()];
						bi.read(bytes,0,(int)ze2.getSize());
						InputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
						MultipartFile multipartFile = new MockMultipartFile(fileName,fileName,"application/pdf",byteArrayInputStream);
						multipartFileReportList.add(multipartFile);
						byteArrayInputStream.close();
					}
				}
			}

			//整体循环结束 保存最后一个子文件夹内容
			//保存 图片list
			addPicList(jsonGuid,multipartFilePngList);
			//保存 报告list
			addReportList(jsonGuid,multipartFileReportList);
			//保存 隐患点list
			dataMngJsonDangerService.saveBatch(dangers);
			//保存 交跨点list
			dataMngJsonCrossService.saveBatch(crosses);

		} catch (IOException eee) {
			log.error(eee.getMessage());
		} finally {
			if (bi!=null){
				try {
					bi.close();
				} catch (IOException eee) {
					eee.printStackTrace();
				}
			}
			file1.delete();
			try {
				zf.close();
			} catch (IOException ex) {
				throw new RuntimeException(ex);
			}
		}

	}

	private void addReportList(String jsonGuid, List<MultipartFile> multipartFileReportList) {
		List<DataMngJsonReport> list = new ArrayList<>();
		multipartFileReportList.forEach(e->{
			DataMngJsonReport report = new DataMngJsonReport();
			report.setJsonGuid(jsonGuid);
			R<AllcoreFileVO> r = ossClient.putFileAttach(BizEnum.BIZ_CODE_DATA_MNG_UPLOAD.getCode(),e,"", "");
			if(r.isSuccess()){
				report.setReportGuid(r.getData().getFileGuid());
				list.add(report);
			}

		});
		dataMngJsonReportService.saveBatch(list);
	}

	private void addPicList(String jsonGuid, List<MultipartFile> multipartFilePngList) {
		List<DataMngJsonPic> list = new ArrayList<>();
		multipartFilePngList.forEach(e->{
			DataMngJsonPic pic = new DataMngJsonPic();
			pic.setJsonGuid(jsonGuid);
			R<AllcoreFileVO> r = ossClient.putFileAttach(BizEnum.BIZ_CODE_DATA_MNG_UPLOAD.getCode(),e,"", "");
			if(r.isSuccess()){
				pic.setPicGuid(r.getData().getFileGuid());
				pic.setPicOriginalName(e.getOriginalFilename());
				list.add(pic);
			}
		});
		dataMngJsonPicService.saveBatch(list);
	}

	@Override
	public List<String> getCrossTypeList() {
		return dataMngJsonCrossService.getCrossTypeList();
	}

	@Override
	public List<String> getDangerTypeList() {
		return dataMngJsonDangerService.getDangerTypeList();
	}

	@Override
	public Boolean uploadReport(MultipartFile file, String reportType, String lineName,String startTowerNo,String endTowerNo) {

		DataMngLineReport lineReport = new DataMngLineReport();
		lineReport.setLineName(lineName);
		lineReport.setReportType(reportType);
		lineReport.setStartTowerNo(startTowerNo);
		lineReport.setEndTowerNo(endTowerNo);
		R<AllcoreFileVO> r = ossClient.putFileAttach(BizEnum.BIZ_CODE_DATA_MNG_UPLOAD.getCode(),file,"", "");
		if(r.isSuccess()){
			lineReport.setLineReportGuid(r.getData().getFileGuid());
		}
		return dataMngLineReportService.save(lineReport);
	}

	@Override
	public IPage<DataMngLineReportVO> lineReportPage(String lineName, String reportType, IPage<DataMngLineReportVO> page) {
		List<DataMngLineReportVO> rst = dataMngLineReportMapper.lineReportPage( lineName,  reportType,page);

		//追加文件全路径
		List<String> reportFileGuids = rst.stream().map(DataMngLineReportVO::getLineReportGuid).collect(Collectors.toList());
		R<List<AllcoreFileVO>> reportRst = ossClient.getFilesDetail(reportFileGuids);
		if (reportRst.isSuccess()) {
			rst.forEach(reportVO->{
				reportRst.getData().forEach(report->{
					if(report.getFileGuid().equals(reportVO.getLineReportGuid())){
						reportVO.setFilePath(report.getStaticPath());
						reportVO.setReportFileName(report.getOriginalName());
					}
				});
			});
		}
		return page.setRecords(rst);

	}
}
