package com.allcore.platform.service.impl;

import com.allcore.platform.service.ISpecificService;
import com.allcore.platform.entity.Sample;
import com.allcore.platform.entity.Specific;
import com.allcore.platform.vo.SampleVO;
import com.allcore.platform.mapper.SampleMapper;
import com.allcore.platform.service.ISampleService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.allcore.common.base.ZxhcServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 平台模板表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Service
public class SampleServiceImpl extends ZxhcServiceImpl<SampleMapper, Sample> implements ISampleService {

	@Autowired
	ISpecificService specificService;

	@Override
	public IPage<SampleVO> selectSamplePage(IPage<SampleVO> page, SampleVO sample) {
		return page.setRecords(baseMapper.selectSamplePage(page, sample));
	}

	@Override
	public List<SampleVO> listSample() {
		return baseMapper.listSample();
	}

	@Override
	public void deleteChild(String sampleGuid) {
		specificService.remove(new QueryWrapper<Specific>().lambda().eq(Specific::getSampleGuid, sampleGuid));
	}

}
