package com.allcore.platform.service.impl;


import com.allcore.platform.mapper.PlatformInfoMapper;
import com.allcore.platform.service.IPlatformInfoService;
import com.allcore.platform.service.ISampleService;
import com.allcore.platform.service.ISpecificService;
import com.allcore.platform.entity.PlatformInfo;
import com.allcore.platform.entity.Sample;
import com.allcore.platform.entity.Specific;
import com.allcore.platform.vo.PlatformInfoVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.allcore.common.base.ZxhcServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 外部平台集成表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Service
public class PlatformInfoServiceImpl extends ZxhcServiceImpl<PlatformInfoMapper, PlatformInfo> implements IPlatformInfoService {

	@Autowired
    ISampleService sampleService;
	@Autowired
    ISpecificService specificService;

	@Override
	public IPage<PlatformInfoVO> selectPlatformInfoPage(IPage<PlatformInfoVO> page, PlatformInfoVO platformInfo) {
		return page.setRecords(baseMapper.selectPlatformInfoPage(page, platformInfo));
	}

	@Override
	public void removeChild(String platformGuid) {
		List<Sample> list = sampleService.list(new QueryWrapper<Sample>().lambda().eq(Sample::getPlatformGuid, platformGuid));
		if (!CollectionUtils.isEmpty(list)){
			sampleService.remove(new QueryWrapper<Sample>().lambda().eq(Sample::getPlatformGuid, platformGuid));
			List<String> collect = list.stream().map(e -> e.getSampleGuid()).collect(Collectors.toList());
			specificService.remove(new QueryWrapper<Specific>().lambda().in(Specific::getSampleGuid, collect));
		}


	}

}
