package com.allcore.platform.mapper;

import com.allcore.platform.vo.DroneLedgerVo;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;
import com.allcore.system.entity.Dept;

import java.util.List;

@DS("slave")
public interface DroneLedgerMapper {

	/**
	 * 根据sncode 查询机巢信息
	 *
	 * @param snCode
	 * @return
	 */
	List<DroneLedgerVo> selectBySnCode(String snCode);

	/**
	 * 根据deptCode 查询机巢信息
	 * @param deptCode
	 * @return
	 */
	List<DroneLedgerVo> selectInDeptCode(@Param("deptCode") String deptCode);
}
