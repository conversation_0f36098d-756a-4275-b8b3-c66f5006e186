package com.allcore.platform.service;

import com.allcore.platform.dto.AlgorithmDTO;
import com.allcore.platform.dto.AlgorithmLyyDTO;

/**
 * （isc调用）
 * <AUTHOR>
 * @date 2022/08/08 15:26
 * @return
 */
public interface IAlgorithmService {

	/**
	 * （tx算法调用）
	 * <AUTHOR>
	 * @date 2022/09/21 11:24
	 * @param algorithmDTO
	 * @return java.lang.String
	 */
	String invokingAlgorithm(AlgorithmDTO algorithmDTO);
	/**
	 * （联研院算法调用）
	 * <AUTHOR>
	 * @date 2022/09/21 13:32
	 * @param algorithmLyyDTO
	 * @return java.lang.String
	 */
	String invokingAlgorithmLyy(AlgorithmLyyDTO algorithmLyyDTO);


}
