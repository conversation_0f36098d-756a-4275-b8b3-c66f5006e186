package com.allcore.platform.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.utils.AESCrypt;
import com.allcore.core.tool.api.R;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2022/09/29 15:48
 **/
public class ResponseFilter implements Filter {

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws IOException, ServletException {
		ChangeResponseWrapper wrapperResponse = new ChangeResponseWrapper((HttpServletResponse) response);//转换成代理类
		// 这里只拦截返回，直接让请求过去，如果在请求前有处理，可以在这里处理
		filterChain.doFilter(request, wrapperResponse);
		byte[] content = wrapperResponse.getContent();//获取返回值
		//判断是否有值
		if (!(content.length > 0)) {
			return;
		}
		String str = new String(content, "UTF-8");
		String ciphertext = null;
		try {
			R r = JSONObject.parseObject(str, R.class);
			Object data = r.getData();
			if (data == null) {
				r.setData(null);
			} else if (data instanceof String) {
				String encrypt = AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, String.valueOf(data));
				r.setData(encrypt);
			} else if (data instanceof Boolean) {
				if ((Boolean) data) {
					r.setData(AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, "true"));
				} else {
					r.setData(AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, "false"));
				}
			} else {
				String jsonString = JSONObject.toJSONString(r.getData());
				String encrypt = AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, jsonString);
				r.setData(encrypt);
			}
			ciphertext = JSONObject.toJSONString(r);
		} catch (Exception e) {
			e.printStackTrace();
		}
		//把返回值输出到客户端
		ServletOutputStream out = response.getOutputStream();
		out.write(ciphertext.getBytes());
		out.flush();
	}

}
