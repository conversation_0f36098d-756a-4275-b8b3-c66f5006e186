package com.allcore.platform.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpVersion;
import org.apache.commons.httpclient.MultiThreadedHttpConnectionManager;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import com.allcore.common.constant.Constant;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URI;
import java.nio.charset.Charset;
import java.util.*;

/**
 * Http调用工具类
 * 服务调用
 * <AUTHOR>
 * @date 2022/04/11 15:11
 **/
@Data
@Slf4j
public class HttpClientUtil {

	/**
	 * HTTP请求超时时间，单位毫秒，默认30秒
	 */
	public static final int TIMEOUT = 30000;

	/**
	 * 字符编码，默认UTF8
	 */
	public static final String CHARSET = "UTF-8";

	private static MultiThreadedHttpConnectionManager cm = new MultiThreadedHttpConnectionManager();
	static {
		// 单主机最大50个连接
		cm.getParams().setDefaultMaxConnectionsPerHost(50);
		// 所有主机合计5000连接
		cm.getParams().setMaxTotalConnections(5000);
	}

	/**
	 *  通过clientIp伪造的客户端请求BufferedImage
	 * @param url
	 * @param clientIp
	 * @return
	 */
	public static BufferedImage getBufferedImageFromUrl(String url,String clientIp) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			httpPost.addHeader("x-forwarded-for",clientIp);
			httpPost.addHeader("typeFlg", "9");

			// 执行http请求
			response = httpClient.execute(httpPost);
			HttpEntity entity = response.getEntity();
			InputStream inputStream = entity.getContent();
			return ImageIO.read(inputStream);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpClient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}


	public static String doGet(String url, Map<String, String> param) {
		// 创建Httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();

		String resultString = "";
		CloseableHttpResponse response = null;
		try {
			// 创建uri
			URIBuilder builder = new URIBuilder(url);
			if (param != null) {
				for (String key : param.keySet()) {
					builder.addParameter(key, param.get(key));
				}
			}
			URI uri = builder.build();
			log.info("调用get接口地址：" + uri);

			// 创建http GET请求
			HttpGet httpGet = new HttpGet(uri);

			// 执行请求
			response = httpclient.execute(httpGet);
			// 判断返回状态是否为200
			if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}

	public static String doGetHaveHeader(String url, Map<String, String> param, Map<String, String> header) {
		log.info("请求地址：{}", url);
		// 创建Httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();

		String resultString = "";
		CloseableHttpResponse response = null;
		try {
			// 创建uri
			URIBuilder builder = new URIBuilder(url);
			if (!param.isEmpty()) {
				for (String key : param.keySet()) {
					log.info("【paramkey:{}，paramvalue：{}】", key, param.get(key));
					builder.addParameter(key, param.get(key));
				}
			}

			URI uri = builder.build();

			// 创建http GET请求
			HttpGet httpGet = new HttpGet(uri);

			for(String key : header.keySet()){
				log.info("【headerkey:{}，headervalue：{}】", key, header.get(key));
				httpGet.setHeader(key, header.get(key));
			}

			// 执行请求
			response = httpclient.execute(httpGet);
			// 判断返回状态是否为200
			if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
				resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpclient.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}

	public static String doGet(String url) {
		return doGet(url, null);
	}

	public static String doPost(String url, Map<String, String> param) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;

		String resultString = "";
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
			// 创建参数列表
			if (param != null) {
				List<NameValuePair> paramList = new ArrayList<>();
				for (String key : param.keySet()) {
					paramList.add(new BasicNameValuePair(key, param.get(key)));
				}
				// 模拟表单
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList, Constant.CURRENCY_UTF);
				httpPost.setEntity(entity);
			}
			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), Constant.CURRENCY_UTF);
		} catch (Exception e) {
			log.error("【http调用异常】- {}", e);
			e.printStackTrace();
		} finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					log.error("【http调用异常】- {}", e);
				}
			}
		}

		return resultString;
	}



	public static String doPost(String url, Map<String, String> param, Map<String, String> header) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;

		String resultString = "";
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");
			for (String key : header.keySet()) {
				httpPost.setHeader(key, header.get(key));
			}
			// 创建参数列表
			if (param != null) {
				List<NameValuePair> paramList = new ArrayList<>();
				for (String key : param.keySet()) {
					paramList.add(new BasicNameValuePair(key, param.get(key)));
				}
				// 模拟表单
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList, Constant.CURRENCY_UTF);
				httpPost.setEntity(entity);
			}
			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), Constant.CURRENCY_UTF);
		} catch (Exception e) {
			log.error("【http调用异常】- {}", e);
			e.printStackTrace();
		} finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					log.error("【http调用异常】- {}", e);
				}
			}
		}

		return resultString;
	}

	/**
	 * @param url            地址
	 * @param param          参数
	 * @param connTimeOut    链接超时时间（三次握手）单位ms
	 * @param requestTimeout 从池里获取链接超时时间 单位ms
	 * @param socketTimeout  返回数据超时时间 单位ms
	 * @return
	 */
	public static String doPost(String url, Map<String, String> param, int connTimeOut, int requestTimeout, int socketTimeout) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;

		String resultString = "";
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			RequestConfig rc = RequestConfig.custom().setConnectTimeout(connTimeOut).setConnectionRequestTimeout(requestTimeout).setSocketTimeout(socketTimeout).build();
			httpPost.setConfig(rc);
			httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8");

			// 创建参数列表
			if (param != null) {
				List<NameValuePair> paramList = new ArrayList<>();
				for (String key : param.keySet()) {
					paramList.add(new BasicNameValuePair(key, param.get(key)));
				}
				// 模拟表单
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList, Constant.CURRENCY_UTF);
				httpPost.setEntity(entity);
			}
			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), Constant.CURRENCY_UTF);
		} catch (Exception e) {
			log.error("【http调用异常】- {}", e);
			e.printStackTrace();
			return "";
		} finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					log.error("【http调用异常】- {}", e);
				}
			}
		}

		return resultString;
	}

	public static String doPost(String url) {
		return doPost(url, null);
	}


	/**
	 *
	 * <AUTHOR>
	 * @date 2022/04/16 13:31
	 * @param url
	 * @param object
	 * @return com.alibaba.fastjson.JSONObject
	 */
	public static JSONObject doPostByJson(String url, Object object){
		JSONObject json = (JSONObject) JSONObject.toJSON(object);
		CloseableHttpClient httpclient = HttpClientBuilder.create().build();
		HttpPost post = new HttpPost(url);
		JSONObject response = null;
		try {
			System.out.println("同步数据接口："+url+"\n"+ JSON.toJSONString(json));
			//解决中文问题。
			post.addHeader("Content-type","application/json; charset=utf-8");
			post.setHeader("Accept", "application/json");
			System.out.println("============================================"+json.toString());
			post.setEntity(new StringEntity(json.toString(), Charset.forName("UTF-8")));

			HttpResponse res = httpclient.execute(post);
			if(res.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
				String result = EntityUtils.toString(res.getEntity());
				response = JSONObject.parseObject(result);
			}
		} catch (Exception e) {
			throw new RuntimeException(url,e);
		}
		return response;
	}


	public static String doPostJson(String url, String json) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		log.info("调用post接口：" + url);
		CloseableHttpResponse response = null;
		String resultString = "";
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			// 创建请求内容
			StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
			httpPost.setEntity(entity);
			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), "utf-8");
		} catch (Exception e) {

			e.printStackTrace();
		} finally {
			try {
				if (null != response) {
				  response.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}




	public static String doPostJsonWithHeaderAuth(String url,Map<String,String> headerMap, String json){
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		String resultString = "";
		try {
			HttpPost httpPost = new HttpPost(url);
			for (String key : headerMap.keySet()) {
				httpPost.setHeader(key, headerMap.get(key));
			}
			List<NameValuePair> params = new ArrayList<NameValuePair>();
			params.add(new BasicNameValuePair("json", json));
			httpPost.setEntity(new UrlEncodedFormEntity(params, "utf-8"));
			RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(29 * 1000)
					.setConnectionRequestTimeout(29 * 1000)
					.setSocketTimeout(29 * 1000).build();
			httpPost.setConfig(requestConfig);
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
		} catch (Exception  e) {
			e.printStackTrace();
		} finally {
			try {
				if (response != null) {
					response.close();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return resultString;
	}

	/**
	 * 新i国网获取token换取票据
	 * <AUTHOR>
	 * @date 2022/04/20 15:40
	 * @param url
	 * @param params
	 * @return com.alibaba.fastjson.JSONObject
	 */
	public static JSONObject doPostByJson(String url, Map<String, String> params,String appId,String signData){
		String response = "";
		InputStream responseBodyAsStream = null;
		BufferedReader bufferedReader = null;
		PostMethod postMethod = null;
		try {
			HttpClient client = new HttpClient(cm);

			client.getHttpConnectionManager().getParams()
				.setConnectionTimeout(TIMEOUT);

			client.getHttpConnectionManager().getParams().setSoTimeout(TIMEOUT);
			postMethod = new PostMethod(url);
			postMethod.getParams().setSoTimeout(TIMEOUT);
			postMethod.getParams().setContentCharset(CHARSET);
			postMethod.getParams().setVersion(HttpVersion.HTTP_1_1);
			postMethod.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"UTF-8");

			if (null != params && !(params.isEmpty())) {
				Iterator<Map.Entry<String, String>> iter = params.entrySet().iterator();
				System.out.println("Charset.defaultCharset():"+Charset.defaultCharset());
			}
			postMethod.addRequestHeader("Content-Type","application/json");
			postMethod.addRequestHeader("X-Acloud-Data-Sign",signData);
			postMethod.addRequestHeader("X-Clientid", appId);
			client.executeMethod(postMethod);

			int statusCode = postMethod.getStatusCode();

			if (HttpStatus.SC_OK == statusCode) {
				responseBodyAsStream =  postMethod.getResponseBodyAsStream();
				bufferedReader = new BufferedReader(new InputStreamReader(responseBodyAsStream));
				StringBuffer buffer = new StringBuffer();
				String result = "";
				while((result = bufferedReader.readLine()) != null) {
					buffer.append(result);
				}
				response = buffer.toString();
				JSONObject object = JSON.parseObject(buffer.toString());
				return object;
			}
		}catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != postMethod) {
				postMethod.releaseConnection();
			}
			IOUtils.closeQuietly(bufferedReader);
			IOUtils.closeQuietly(responseBodyAsStream);
		}
		return null;
	}

	/**
	 * 新i国网单点票据换取用户接口
	 * <AUTHOR>
	 * @date 2022/04/20 15:40
	 * @param url
	 * @param param
	 * @return com.alibaba.fastjson.JSONObject
	 */
	public static JSONObject doPostByTicket(String url, Map<String, String> param,String appId,String signData,String token){
		String response = "";
		InputStream responseBodyAsStream = null;
		BufferedReader bufferedReader = null;
		PostMethod postMethod = null;
		try {
			HttpClient client = new HttpClient(cm);

			client.getHttpConnectionManager().getParams()
				.setConnectionTimeout(TIMEOUT);

			client.getHttpConnectionManager().getParams().setSoTimeout(TIMEOUT);
			postMethod = new PostMethod(url);
			postMethod.getParams().setSoTimeout(TIMEOUT);
			postMethod.getParams().setContentCharset(CHARSET);
			postMethod.getParams().setVersion(HttpVersion.HTTP_1_1);
			postMethod.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET,"UTF-8");

			// 设置头信息
			postMethod.addRequestHeader("Content-Type","application/json");
			postMethod.addRequestHeader("X-ISC-AccessToken","Client "+token);
			postMethod.addRequestHeader("X-Acloud-Data-Sign", signData);
			postMethod.addRequestHeader("X-Clientid", appId);
			client.executeMethod(postMethod);

			int statusCode = postMethod.getStatusCode();

			if (HttpStatus.SC_OK == statusCode) {
				responseBodyAsStream =  postMethod.getResponseBodyAsStream();
				bufferedReader = new BufferedReader(new InputStreamReader(responseBodyAsStream));
				StringBuffer buffer = new StringBuffer();
				String result = "";
				while((result = bufferedReader.readLine()) != null) {
					buffer.append(result);
				}
				response = buffer.toString();
				log.info("ticket获取的信息==========:"+response);
				JSONObject object = JSON.parseObject(buffer.toString());
				return object;
			}
		}catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (null != postMethod) {
				postMethod.releaseConnection();
			}
			IOUtils.closeQuietly(bufferedReader);
			IOUtils.closeQuietly(responseBodyAsStream);
		}
		return null;
	}

}
