package com.allcore.platform.interceptor;


import com.alibaba.fastjson.JSONObject;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.utils.AESCrypt;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.StreamUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;

//更改body中的值
@Slf4j
public class BodyRequestWrapper  extends HttpServletRequestWrapper {
	private byte[] requestBody;
	private Charset charSet;

	public BodyRequestWrapper(HttpServletRequest request) {
		super(request);

		//缓存请求body
		try {
			String requestBodyStr = getRequestPostStr(request);
			if (StringUtils.isNotBlank(requestBodyStr)) {
				JSONObject jsonObject = null;
				try {
						String decrypt = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, requestBodyStr);
						jsonObject = JSONObject.parseObject(decrypt);
					} catch (Exception e) {
						log.error("转换json出错");
					}

				//存疑
//				Object[] obj = jsonObject.keySet().toArray();
//				for (Object o : obj) {
//					jsonObject.put((String) o, StringUtils.trimToNull(jsonObject.get(o).toString()));
//				}


				requestBody = jsonObject.toString().getBytes(charSet);
			} else {
				requestBody = new byte[0];
			}
		} catch (Exception e) {
			log.error("", e);
		}
	}

	public String getRequestPostStr(HttpServletRequest request)
		throws IOException {
		String charSetStr = request.getCharacterEncoding();
		if (charSetStr == null) {
			charSetStr = "UTF-8";
		}
		charSet = Charset.forName(charSetStr);

		return StreamUtils.copyToString(request.getInputStream(), charSet);
	}

	/**
	 * 重写 getInputStream()
	 */
	@Override
	public ServletInputStream getInputStream() {
		if (requestBody == null) {
			requestBody = new byte[0];
		}

		final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(requestBody);

		return new ServletInputStream() {
			@Override
			public boolean isFinished() {
				return false;
			}

			@Override
			public boolean isReady() {
				return false;
			}

			@Override
			public void setReadListener(ReadListener readListener) {

			}

			@Override
			public int read() {
				return byteArrayInputStream.read();
			}
		};
	}

	/**
	 * 重写 getReader()
	 */
	@Override
	public BufferedReader getReader() {
		return new BufferedReader(new InputStreamReader(getInputStream()));
	}
}
