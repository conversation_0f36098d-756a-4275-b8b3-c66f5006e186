package com.allcore.main.code.station.controller;

import javax.validation.Valid;

import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.station.dto.SubstationDTO;
import com.allcore.main.code.station.vo.StationVO;
import org.springframework.web.bind.annotation.*;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.station.dto.StationManagementDTO;
import com.allcore.main.code.station.dto.StationManagementSaveDTO;
import com.allcore.main.code.station.service.IStationManagementService;
import com.allcore.main.code.station.vo.StationManagementVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@RestController
@AllArgsConstructor
@RequestMapping("/stationmanagement")
@Api(value = "场站管理接口", tags = "场站管理接口")
public class StationManagementController extends ZxhcController {

    private final IStationManagementService stationManagementService;

    /**
     * 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页", notes = "传入stationManagement")
    public R<IPage<StationManagementVO>> page(StationManagementDTO stationManagement, Query query) {
        IPage<StationManagementVO> pages =
            stationManagementService.selectStationManagementPage(Condition.getPage(query), stationManagement);
        return R.data(pages);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "修改", notes = "传入stationManagement")
    public R update(@Valid @RequestBody StationManagementSaveDTO stationManagement) {
        return stationManagementService.updateStationManagement(stationManagement);
    }

    @GetMapping("/inspectionTree")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "巡检路线设备树", notes = "传入stationManagement")
    public R<List<?>> inspectionTree(SubstationDTO dto) {
        List<StationVO> list=stationManagementService.getInspectionTree(dto);
        if(Func.isNull(list)){
            return R.fail("站点相关信息不存在!");
        }
        if(Func.isEmpty(dto.getDeptId())){
            return R.data(list);
        }
        StationVO vo=list.stream().filter(sub-> Objects.equals(sub.getDeptId(),dto.getDeptId())).findFirst().orElse(null);
        if(Func.notNull(vo)){
            return R.data(vo.getChildren());
        }
        return R.data(Collections.emptyList());
    }


}
