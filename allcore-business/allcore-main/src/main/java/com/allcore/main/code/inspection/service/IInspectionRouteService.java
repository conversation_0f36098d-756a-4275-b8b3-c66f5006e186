package com.allcore.main.code.inspection.service;

import com.allcore.core.tool.api.R;
import com.allcore.main.code.inspection.dto.InspectionRouteDTO;
import com.allcore.main.code.inspection.dto.InspectionRouteSaveDTO;
import com.allcore.main.code.inspection.entity.InspectionRoute;
import com.allcore.main.code.inspection.vo.InspectionRouteVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.Valid;
import java.util.List;

public interface IInspectionRouteService extends IService<InspectionRoute> {
    R saveInspectionRoute(@Valid InspectionRouteSaveDTO dto);

    R<IPage<InspectionRouteVO>> selectInspectionRoutePage(IPage<InspectionRouteVO> page, InspectionRouteDTO dto);

    R updateInspectionRoute(@Valid InspectionRouteSaveDTO dto);

    R deleteInspectionRoute(List<String> strList);

}
