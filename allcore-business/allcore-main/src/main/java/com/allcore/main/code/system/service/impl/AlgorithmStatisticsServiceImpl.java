package com.allcore.main.code.system.service.impl;

import com.allcore.common.enums.BizDictEnum;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.main.code.system.dto.AlgorithmStatisticsDTO;
import com.allcore.main.code.system.entity.AlgorithmStatistics;
import com.allcore.main.code.system.mapper.AlgorithmStatisticsMapper;
import com.allcore.main.code.system.service.IAlgorithmStatisticsService;
import com.allcore.main.code.system.vo.AlgorithmStatisticsVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 算法统计 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Service
public class AlgorithmStatisticsServiceImpl extends ServiceImpl<AlgorithmStatisticsMapper, AlgorithmStatistics> implements IAlgorithmStatisticsService {

	@Override
	public IPage<AlgorithmStatisticsVO> selectAlgorithmStatisticsPage(IPage<AlgorithmStatisticsVO> page, AlgorithmStatisticsDTO algorithmStatistics) {
		List<AlgorithmStatisticsVO> list = baseMapper.selectAlgorithmStatisticsPage(page, algorithmStatistics);
		list.forEach(e -> e.setDeviceTypeName(DictBizCache.getValue(BizDictEnum.DEVICE_TYPE.getCode(),e.getDeviceType())));
		return page.setRecords(list);
	}

}
