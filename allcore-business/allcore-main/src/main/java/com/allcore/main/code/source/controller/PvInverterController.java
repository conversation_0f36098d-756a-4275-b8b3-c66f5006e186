package com.allcore.main.code.source.controller;

import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.common.dto.IdsDTO;
import com.allcore.main.code.source.dto.PvAreaDTO;
import com.allcore.main.code.source.dto.PvInverterDTO;
import com.allcore.main.code.source.service.IPvInverterService;
import com.allcore.main.code.source.vo.PvInverterVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Value;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @program: bl
 * @description: 光伏逆变器接口类
 * @author: fanxiang
 * @create: 2025-05-22 14:56
 **/

@RestController
@RequestMapping("/inverter")
@AllArgsConstructor
@Api(value="光伏逆变器接口",tags = "光伏逆变器")
public class PvInverterController {

    private final IPvInverterService pvInverterService;

    @PostMapping("/save")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "逆变器新增")
    public R save(@Valid  @RequestBody PvInverterDTO pvInverterDTO){
        return pvInverterService.savePvInverter(pvInverterDTO);
    }

    @GetMapping("/page")
    @ApiOperationSupport(order=2)
    @ApiOperation(value = "逆变器分页查询")
    public R<IPage<PvInverterVO>> page(PvInverterDTO pvInverterDTO, Query query){
        IPage<PvInverterVO> pages=pvInverterService.selectPvInverterPage(pvInverterDTO, Condition.getPage(query));
        return R.data(pages);
    }

    @PostMapping("/detail")
    @ApiOperationSupport(order=6)
    @ApiOperation(value = "逆变器详情")
    public R<PvInverterVO> detail(@RequestBody PvInverterDTO dto){
        return pvInverterService.getInverterDetail(dto);
    }

    @PostMapping("/update")
    @ApiOperationSupport(order=3)
    @ApiOperation(value = "逆变器修改")
    public R update(@Valid @RequestBody PvInverterDTO pvInverterDTO){
        return pvInverterService.updatePvInverter(pvInverterDTO);
    }

    @PostMapping("/delete")
    @ApiOperationSupport(order=4)
    @ApiOperation(value = "逆变器删除")
    public R remove(@RequestBody IdsDTO dto){
        return pvInverterService.deletePvInverter(Func.toStrList(dto.getIds()));
    }

    @GetMapping("/getAllModel")
    @ApiOperationSupport(order=5)
    @ApiOperation(value = "获取所有逆变器型号")
    public R<List<String>> getAllModel(){
        return  pvInverterService.getAllModel();
    }

    @PostMapping("/import/excel")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "逆变器-导入", notes = "逆变器导入")
    public R importExcel(@NotNull(message = "导入文件不能为空") MultipartFile file) throws Exception {
        return pvInverterService.importExcel(file);
    }

    @PostMapping("/downTemplate")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "模板下载", notes = "逆变器导入模板下载")
    public void downTemplate(ModelMap modelMap, HttpServletRequest request,
                             HttpServletResponse response) {
        pvInverterService.downTemplate(modelMap, request, response);
    }

}
