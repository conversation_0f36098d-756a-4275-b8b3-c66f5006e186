package com.allcore.main.code.flywatch.wrapper;


import com.allcore.common.enums.BizDictEnum;
import com.allcore.core.mp.support.BaseEntityWrapper;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.main.code.flywatch.vo.AirspaceVO;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;

import java.util.ArrayList;
import java.util.List;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class AirspaceWrapper extends BaseEntityWrapper<AirspaceVO, AirspaceVO> {

    public static AirspaceWrapper build() {
        return new AirspaceWrapper();
    }

    @Override
    public AirspaceVO entityVO(AirspaceVO vo) {
        vo.setAirspaceTypeZh(DictBizCache.getValue(BizDictEnum.AIRSPACE_TYPE.getCode(), vo.getAirspaceType()));
        vo.setFileStatusZh(DictBizCache.getValue(BizDictEnum.FILE_STATUS.getCode(), vo.getFileStatus()));
        if(StringUtil.isNotBlank(vo.getUavTypes())){
            List<String> uavTypeList = new ArrayList<>();
            Func.toStrList(vo.getUavTypes()).forEach(e->{
                uavTypeList.add(DictBizCache.getValue(BizDictEnum.UAV_TYPE.getCode(), e));
            });
            vo.setUavTypesZh(String.join(StringPool.COMMA,uavTypeList));
        }
        Dept dept = SysCache.getDeptByDeptCode(vo.getDeptCode());
        vo.setDeptName(dept.getDeptName());
        vo.setParentDeptName(SysCache.getDeptName(dept.getParentId()));

        return vo;
    }

}
