package com.allcore.main.code.station.service;

import com.allcore.app.code.flightsorties.vo.AppStatisticVO;
import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.station.dto.StationManagementDTO;
import com.allcore.main.code.station.dto.StationManagementSaveDTO;
import com.allcore.main.code.station.dto.SubstationDTO;
import com.allcore.main.code.station.entity.StationManagement;
import com.allcore.main.code.station.vo.StationManagementVO;
import com.allcore.main.code.station.vo.StationVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
public interface IStationManagementService extends ZxhcService<StationManagement> {

    /**
     * 自定义分页
     *
     * @param page
     * @param stationManagement
     * @return
     */
    IPage<StationManagementVO> selectStationManagementPage(IPage<StationManagementVO> page,
        StationManagementDTO stationManagement);

    /**
     * 增
     *
     * @param stationManagement
     * @return
     */
    R updateStationManagement(StationManagementSaveDTO stationManagement);

    List<StationVO> getInspectionTree(SubstationDTO dto);

    /**
     * app统计
     * @param map
     * @return
     */
    R<AppStatisticVO> getPvStatistics(Map<String, Object> map);
}
