package com.allcore.main.code.source.mapper;

import com.allcore.main.code.source.entity.UavModelFile;
import com.allcore.main.code.source.vo.UavModelFileVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
public interface UavModelFileMapper extends BaseMapper<UavModelFile> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param uavModelFile
	 * @return
	 */
	List<UavModelFileVO> selectUavModelFilePage(IPage page, UavModelFileVO uavModelFile);

}
