package com.allcore.main.code.inspection.mapper;

import com.allcore.main.code.inspection.dto.InspectionDeviceDetailWithType;
import com.allcore.main.code.inspection.entity.InspectionDeviceDetail;
import com.allcore.main.code.inspection.vo.InspectionDeviceDetailVO;
import com.allcore.main.code.source.vo.DeviceWithParentVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface InspectionDeviceDetailMapper extends BaseMapper<InspectionDeviceDetail> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inspectionDeviceDetail
	 * @return
	 */
	List<InspectionDeviceDetailVO> selectInspectionDeviceDetailPage(IPage page, InspectionDeviceDetailVO inspectionDeviceDetail);

	/**
	 *  mapper层
	 * @param inspectionTaskId
	 * @return
	 */
	List<DeviceWithParentVO> getLineInspectionDeviceDetails(@Param("inspectionTaskId") String inspectionTaskId);

	/**
	 *  mapper层
	 * @param inspectionTaskId
	 * @return
	 */
	List<DeviceWithParentVO> getFanInspectionDeviceDetails(@Param("inspectionTaskId") String inspectionTaskId);

	/**
	 *  mapper层
	 * @param inspectionTaskId
	 * @return
	 */
	List<DeviceWithParentVO> getPvInspectionDeviceDetails(@Param("inspectionTaskId") String inspectionTaskId);

	List<InspectionDeviceDetailWithType> listWithType(@Param("inspectionTaskId") String inspectionTaskId);

	Long countDeviceByPlanIdAndRemoveIds(@Param("planId") String planId, @Param("removeIds") List<String> removeIds);
}
