package com.allcore.main.code.source.controller;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.source.entity.BoosterDevice;
import com.allcore.main.code.source.service.IBoosterDeviceService;
import com.allcore.main.code.source.vo.BoosterInfoVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;


/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2025-05-22
 */
@RestController
@AllArgsConstructor
@RequestMapping("/boosterDevice")
@Api(value = "升压站、储能站设备接口", tags = "升压站、储能站设备接口")
public class BoosterDeviceController extends ZxhcController {

    private final IBoosterDeviceService boosterDeviceService;
    /**
     * 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页", notes = "传入boosterStationVO")
    public R<IPage<BoosterInfoVO>> page(BoosterInfoVO boosterInfoVO, Query query) {
        IPage<BoosterInfoVO> pages =
                boosterDeviceService.selectBoosterDevicePage(Condition.getPage(query), boosterInfoVO);
        return R.data(pages);
    }
    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "新增", notes = "传入boosterDevice")
    public R save(@Valid @RequestBody BoosterDevice boosterDevice) {
        return R.status(boosterDeviceService.saveBoosterDevice(boosterDevice));
    }

    /**
     * 新增
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "修改", notes = "传入boosterDevice")
    public R update(@Valid @RequestBody BoosterDevice boosterDevice) {
        return R.status(boosterDeviceService.updateDevice(boosterDevice));
    }
    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "详情", notes = "传入boosterStation")
    public R<BoosterDevice> detail(@Valid @RequestParam String id) {
        BoosterDevice detail = boosterDeviceService.getById(id);
        return R.data(detail);
    }


}
