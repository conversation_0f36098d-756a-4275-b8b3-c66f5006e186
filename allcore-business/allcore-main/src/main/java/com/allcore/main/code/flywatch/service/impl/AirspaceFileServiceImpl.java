package com.allcore.main.code.flywatch.service.impl;

import com.allcore.main.code.flywatch.entity.AirspaceFile;
import com.allcore.main.code.flywatch.mapper.AirspaceFileMapper;
import com.allcore.main.code.flywatch.service.IAirspaceFileService;
import com.allcore.main.code.flywatch.vo.AirspaceFileVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class AirspaceFileServiceImpl extends ServiceImpl<AirspaceFileMapper, AirspaceFile> implements IAirspaceFileService {

	@Override
	public IPage<AirspaceFileVO> selectAirspaceFilePage(IPage<AirspaceFileVO> page, AirspaceFileVO airspaceFile) {
		return page.setRecords(baseMapper.selectAirspaceFilePage(page, airspaceFile));
	}

}
