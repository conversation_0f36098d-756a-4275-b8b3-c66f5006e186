package com.allcore.main.code.station.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.lang.UUID;
import com.allcore.app.code.flightsorties.vo.AppStatisticVO;
import com.allcore.common.enums.BizDictEnum;

import com.allcore.core.tool.node.ForestNodeMerger;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.external.feign.IAirportTaskClient;
import com.allcore.external.feign.INestClient;
import com.allcore.main.code.source.entity.Fan;
import com.allcore.main.code.source.entity.PvArea;
import com.allcore.main.code.source.service.IFanService;
import com.allcore.main.code.source.service.IPvAreaService;
import com.allcore.main.code.station.dto.SubstationDTO;
import com.allcore.main.code.station.vo.StationTreeVO;
import com.allcore.main.code.station.vo.StationVO;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;

import com.allcore.system.feign.ISysClient;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.CollectionUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.source.vo.RealNameVO;
import com.allcore.main.code.station.dto.StationManagementDTO;
import com.allcore.main.code.station.dto.StationManagementSaveDTO;
import com.allcore.main.code.station.entity.StationManagement;
import com.allcore.main.code.station.mapper.StationManagementMapper;
import com.allcore.main.code.station.service.IStationManagementService;
import com.allcore.main.code.station.vo.StationManagementVO;
import com.allcore.main.code.station.wrapper.StationManagementWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import static com.allcore.common.constant.CommonConstant.*;
import static com.allcore.main.constant.MainConstant.NEIMENGGU_DEPT_ID;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-25
 */
@Service
@AllArgsConstructor
@Slf4j
public class StationManagementServiceImpl extends ZxhcServiceImpl<StationManagementMapper, StationManagement>
    implements IStationManagementService {


    private final IPvAreaService pvAreaService;

    private final IFanService fanService;
    private final INestClient nestClient;

    private ISysClient sysClient;

    @Override
    public IPage<StationManagementVO> selectStationManagementPage(IPage<StationManagementVO> page,
        StationManagementDTO stationManagement) {
        if (Func.isBlank(stationManagement.getDeptCode())) {
            stationManagement.setDeptCode(AuthUtil.getDeptCode());
        }
        List<StationManagementVO> voList = baseMapper.selectStationManagementPage(page, stationManagement);
        if (CollectionUtil.isNotEmpty(voList)) {
            setStationHead(voList);
        }
        return page.setRecords(StationManagementWrapper.build().listVoByVo(voList));
    }

    @Override
    public R updateStationManagement(StationManagementSaveDTO dto) {
        StationManagement entity = BeanUtil.copy(dto, StationManagement.class);
        this.remove(new LambdaQueryWrapper<StationManagement>().eq(StationManagement::getDeptId, entity.getDeptId()));
        // 同步修改dept信息
        Dept dept = SysCache.getDept(entity.getDeptId());
        dept.setCapacity(dto.getStationInstall());
        dept.setAddress(dto.getStationAddress());
        //todo 修改单位区域


        sysClient.deptSave(dept);

        return R.status(this.save(entity));
    }



    private void setStationHead(List<StationManagementVO> voList) {
        // 部门deptids
        List<String> deptIds = voList.stream().map(m -> m.getDeptId()).collect(Collectors.toList());

        List<RealNameVO> realNameVOList = baseMapper.selectRealName(deptIds);
        if (CollectionUtils.isNotEmpty(realNameVOList)) {
            voList.stream().peek(vo -> vo.setStationHead(getRealNameMap(realNameVOList).get(vo.getDeptId())))
                .collect(Collectors.toList());
        }

    }

    private Map<String, String> getRealNameMap(List<RealNameVO> realNameVOList) {
        return realNameVOList.stream().collect(Collectors.toMap(RealNameVO::getDeptId, RealNameVO::getStationHead));
    }


    /**
     * （巡检路线设备树）
     * @param dto
     * @return
     */
    @Override
    public List<StationVO> getInspectionTree(SubstationDTO dto) {

        // 是否带入站点
        List<Dept> deptList = null;
        if (Func.isEmpty(dto.getDeptId())) {
            R<List<Dept>> deptChildR = sysClient.getDeptChild(NEIMENGGU_DEPT_ID);
            if(deptChildR.isSuccess()&&Func.notNull(deptChildR.getData())) {
                deptList=deptChildR.getData();
            }
        } else {
            Dept dept = SysCache.getDept(dto.getDeptId());
            if (Func.notNull(dept)) {
                deptList = Collections.singletonList(dept);
            }
        }
        if (Func.isEmpty(deptList)) {
            return null;
        }
        if (Func.isBlank(dto.getDeptCode())) {
            dto.setDeptCode(AuthUtil.getDeptCode());
        }

        List<String> deviceType = null;
        if (Func.isNotBlank(dto.getDeviceType())) {
            deviceType = Func.splitTrim(dto.getDeviceType(), ',');
        }

        List<StationTreeVO> bTreeList = new ArrayList<>();

        List<String>stationIds=new ArrayList<>();

        //光伏区域
        if (deviceType != null && deviceType.contains(BizDictEnum.INSPECTION_TYPE_PV.getCode())) {

            //是否加载设备
            if (dto.isLazy()) {
                List<PvArea> list = pvAreaService.list(Wrappers.<PvArea>lambdaQuery()
                        .likeRight(Func.isNotBlank(dto.getDeptCode()), PvArea::getDeptCode, dto.getDeptCode())
                        .orderByAsc(PvArea::getDeviceName));

                //查询光伏区域的所有场站
                // todo 现阶段光伏风机台账都在一个站（电站类型为风机)，暂定为同一个
                deptList.stream().filter(x -> x.getDeptCategory() != null && x.getDeptCategory().equals(3)).forEach(x -> {
                    StationTreeVO vo = new StationTreeVO();
                    vo.setCode(BizDictEnum.INSPECTION_TYPE_PV.getCode());
                    vo.setName("光伏区域");
                    vo.setStationId(x.getId());
                    vo.setDeptCode(x.getDeptCode());
                    vo.setId(UUID.fastUUID().toString());
                    vo.setLevel(1);
                    vo.setChildren(list.stream().filter(pv -> Objects.equals(pv.getDeptCode(), x.getDeptCode()))
                            .map(sv -> {
                                StationTreeVO vo1 = new StationTreeVO();
                                vo1.setName(sv.getDeviceName());
                                vo1.setId(sv.getId());
                                vo1.setParentId(vo.getId());
                                vo1.setLevel(2);
                                vo1.setStationId(vo.getStationId());
                                vo1.setDeptCode(sv.getDeptCode());
                                return vo1;
                            }).collect(Collectors.toList()));
                    stationIds.add(x.getId());
                    bTreeList.add(vo);
                });
            }
        }

        // 风机
        if (deviceType != null && deviceType.contains(BizDictEnum.INSPECTION_TYPE_FAN.getCode())) {

            //是否加载设备
            if (dto.isLazy()) {
                List<Fan> list = fanService.list(Wrappers.<Fan>lambdaQuery()
                        .likeRight(Func.isNotBlank(dto.getDeptCode()), Fan::getDeptCode, dto.getDeptCode())
                        .orderByAsc(Fan::getDeviceName));

                deptList.stream().filter(x -> x.getDeptCategory() != null && x.getDeptCategory().equals(3)).forEach(x -> {
                    StationTreeVO vo = new StationTreeVO();
                    vo.setCode(BizDictEnum.INSPECTION_TYPE_FAN.getCode());
                    vo.setName("风机");
                    vo.setStationId(x.getId());
                    vo.setDeptCode(x.getDeptCode());
                    vo.setId(UUID.fastUUID().toString());
                    vo.setLevel(1);
                    vo.setChildren(list.stream().filter(fan -> Objects.equals(fan.getDeptCode(), x.getDeptCode()))
                            .map(sv -> {
                                StationTreeVO vo1 = new StationTreeVO();
                                vo1.setName(sv.getDeviceName());
                                vo1.setId(sv.getId());
                                vo1.setParentId(vo.getId());
                                vo1.setLevel(2);
                                vo1.setStationId(vo.getStationId());
                                vo1.setDeptCode(sv.getDeptCode());
                                return vo1;
                            }).collect(Collectors.toList()));
                    stationIds.add(x.getId());
                    bTreeList.add(vo);
                });
            }
        }


        // 将结果组装成树节点
        List<StationTreeVO> mTreeList = ForestNodeMerger.merge(bTreeList);
        //将区域作为根节点添加到树节点中
        List<StationVO>voList=new ArrayList<>();
        deptList.stream().sorted(Comparator.comparing(Dept::getSort)).forEach(dept -> {
            if(stationIds.contains(dept.getId())){
                StationVO vo = BeanUtil.copy(dept, StationVO.class);
                if (vo != null) {
                    vo.setChildren(mTreeList.stream().filter(tree->Objects.equals(dept.getId(),tree.getStationId())).collect(Collectors.toList()));
                }
                voList.add(vo);
            }
        });
        return voList;
    }

    @Override
    public R<AppStatisticVO> getPvStatistics(Map<String, Object> params) {
        // 初始化返回对象
        AppStatisticVO vo = new AppStatisticVO();

        // 1. 获取PV统计数据
        R<Map<String, Object>> response = nestClient.getPvStatistics();
        if (response != null && response.isSuccess() && response.getData() != null) {
            Map<String, Object> data = response.getData();
            vo.setDaySum(getSafeString(data, DAY_SUM));
            vo.setTotalSum(getSafeString(data, TOTAL_SUM));
            vo.setCapacity(getSafeString(data, CAPACITY));
        }

        // 2. 处理部门特殊容量
        String deptId = getSafeString(params, "deptId");
        if (StringUtil.isNotBlank(deptId)) {
            StationManagement station = getOne(new LambdaQueryWrapper<StationManagement>()
                    .eq(StationManagement::getDeptId, deptId));
            if (station != null && station.getStationInstall() != null) {
                vo.setCapacity(station.getStationInstall());
            }
        }

        return R.data(vo);
    }

    // 安全获取Map中的String值
    private String getSafeString(Map<String, Object> map, String key) {
        return map != null && map.get(key) != null ? map.get(key).toString() : null;
    }
}