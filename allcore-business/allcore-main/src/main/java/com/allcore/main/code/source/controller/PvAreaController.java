package com.allcore.main.code.source.controller;

import javax.validation.Valid;

import com.allcore.main.code.flywatch.dto.UploadDevicePvAirLineDTO;
import com.allcore.main.code.flywatch.service.IRouteService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.common.dto.IdsDTO;
import com.allcore.main.code.source.dto.PvAreaDTO;
import com.allcore.main.code.source.dto.PvAreaSaveDTO;
import com.allcore.main.code.source.service.IPvAreaService;
import com.allcore.main.code.source.vo.PvAreaVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;

/**
 * 光伏区域 控制器
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/pvarea")
@Api(value = "光伏区域接口", tags = "光伏管理")
public class PvAreaController extends ZxhcController {
    private final IRouteService routeService;
    private final IPvAreaService pvAreaService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "区域-详情", notes = "传入pvArea")
    public R<PvAreaVO> detail(String id) {

        return pvAreaService.getPvAreaOne(id);
    }

    /**
     * 自定义分页
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "区域-分页", notes = "传入pvArea")
    public R<IPage<PvAreaVO>> page(PvAreaDTO pvArea, Query query) {
        IPage<PvAreaVO> pages = pvAreaService.selectPvAreaPage(Condition.getPage(query), pvArea);
        return R.data(pages);
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "区域-新增", notes = "传入pvArea")
    public R save(@Valid @RequestBody PvAreaSaveDTO pvArea) {
        return pvAreaService.savePvArea(pvArea);
    }

    /**
     * 修改
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "区域-修改", notes = "传入pvArea")
    public R update(@Valid @RequestBody PvAreaSaveDTO pvArea) {
        return pvAreaService.updatePvAreaById(pvArea);
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "区域-删除", notes = "传入ids")
    public R remove(@Valid @RequestBody IdsDTO dto) {
        return pvAreaService.deletePvArea(Func.toStrList(dto.getIds()));
    }

    /**
     * 导入光伏区域文件
     */
    @PostMapping("/importPVArea")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "区域-算法文件导入", notes = "导入光伏区域文件")
    public R importPvArea(@RequestParam("file") MultipartFile file) {
        return pvAreaService.importPvArea(file);
    }

    /**
     * 矢量化数据推送
     */
    @PostMapping(value = "/uploadPvData", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "区域-矢量数据推送", notes = "矢量化数据推送")
    public R uploadPvData(@RequestPart("file") MultipartFile file) {
        return pvAreaService.uploadPvData(file);
    }

    /**
     * 矢量化数据推送
     */
    @PostMapping(value = "/uploadAllPvData", produces = "application/json;charset=UTF-8")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "区域-一次导入所有光伏数据&推送算法侧", notes = "导入加推送")
    public R uploadAllPvData(@RequestPart("file") MultipartFile file) {
        return pvAreaService.uploadAllPvData(file);
    }
    /**
     * 根据部门id获取光伏台账
     * 
     * @param deptId
     * @return
     */
    @GetMapping("/devicePvArea/list")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "根据部门id获取光伏台账", notes = "根据部门id获取光伏台账")
    R devicePvAreaList(@RequestParam String deptId) {
        return pvAreaService.devicePvAreaList(deptId);
    };
    /**
     * 新增/更新航迹文件
     *
     * @param file
     * @param dto
     * @return
     */
    @PostMapping("/devicePvAirLine/uploadDevicePvAirLineFile")
    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "新增/更新航迹文件", notes = "新增/更新航迹文件")
    R uploadDevicePvAirLineFile(@RequestParam("file") MultipartFile file, UploadDevicePvAirLineDTO dto) {
        return routeService.uploadDevicePvAirLineFile(file, dto);
    };

    /**
     * 航迹文件列表查询接口
     *
     * @param devicePvId
     * @param airLineType
     * @return
     */
    @GetMapping("/devicePvAirLine/list")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "航迹文件列表查询接口", notes = "航迹文件列表查询接口")
    R devicePvAirLineList(@RequestParam String devicePvId, @RequestParam String airLineType) {
        return routeService.devicePvAirLineList(devicePvId, airLineType);
    };
}
