package com.allcore.main.code.source.service;

import com.allcore.common.base.ZxhcService;
import com.allcore.core.mp.base.BaseService;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.source.dto.AirportAirplaneDTO;
import com.allcore.main.code.source.dto.AirportAirplaneQueryDTO;
import com.allcore.main.code.source.entity.AirportAirplane;
import com.allcore.main.code.source.vo.AirportAirplaneVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
public interface IAirportAirplaneService extends ZxhcService<AirportAirplane> {


	/**
     * 新增或修改
     * 
     * @param airportAirplane
     * @return
     */
    R saveOrUpdateAirportAirplane(AirportAirplaneDTO airportAirplane);

	/**
	 * 详情
	 * @param airportNestId
	 * @return
	 */
	R  getAirportAirplaneOne(String airportNestId);

	/**
	 * 获取模型tree
	 *
	 * @param dto
	 * @return
	 */
	R getUavModeltree(AirportAirplaneQueryDTO dto);
	/**
	 * 获取无人机tree
	 *
	 * @param dto
	 * @return
	 */
	R getUavtree(AirportAirplaneQueryDTO dto);

	/**
	 * 机场对应的无人机
	 * @param dto
	 * @return
	 */
	R getUavTreeForAirPort(AirportAirplaneQueryDTO dto);
}
