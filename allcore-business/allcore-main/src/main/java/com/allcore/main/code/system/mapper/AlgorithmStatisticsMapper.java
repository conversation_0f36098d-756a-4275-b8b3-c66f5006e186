package com.allcore.main.code.system.mapper;

import com.allcore.main.code.system.dto.AlgorithmStatisticsDTO;
import com.allcore.main.code.system.entity.AlgorithmStatistics;
import com.allcore.main.code.system.vo.AlgorithmStatisticsVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 算法统计 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
public interface AlgorithmStatisticsMapper extends BaseMapper<AlgorithmStatistics> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param algorithmStatistics
	 * @return
	 */
	List<AlgorithmStatisticsVO> selectAlgorithmStatisticsPage(IPage page, @Param("dto") AlgorithmStatisticsDTO algorithmStatistics);

}
