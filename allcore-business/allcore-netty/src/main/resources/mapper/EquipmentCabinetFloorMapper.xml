<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.allcore.netty.mapper.EquipmentCabinetFloorMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.allcore.netty.entity.EquipmentCabinetFloorEntity" id="equipmentCabinetFloorMap">
        <result property="id" column="id"/>
        <result property="floorGuid" column="floor_guid"/>
        <result property="cabinetGuid" column="cabinet_guid"/>
        <result property="equipmentCabinetGuid" column="equipment_cabinet_guid"/>
        <result property="floorType" column="floor_type"/>
        <result property="cabinetModel" column="cabinet_model"/>
        <result property="floorNo" column="floor_no"/>
        <result property="position" column="position"/>
        <result property="status" column="status"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
