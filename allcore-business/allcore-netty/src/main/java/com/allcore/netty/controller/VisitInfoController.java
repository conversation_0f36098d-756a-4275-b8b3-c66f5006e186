package com.allcore.netty.controller;

import com.alibaba.fastjson.JSONArray;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.netty.dto.VisitInfoDTO;
import com.allcore.netty.entity.VisitInfoEntity;
import com.allcore.netty.service.VisitInfoService;
import com.allcore.netty.vo.VisitInfoVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-27 13:50:57
 */
@RestController
@RequestMapping("/visitinfo")
@Api(value = "访问设置接口", tags = "访问设置接口")
public class VisitInfoController {
    @Autowired
    private VisitInfoService visitInfoService;


	@GetMapping("getData")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "查询接口", notes = "查询接口")
	public R<VisitInfoVO> getData() {
		VisitInfoEntity entity = visitInfoService.getOne(new QueryWrapper<>());
		VisitInfoVO visitInfoVo = new VisitInfoVO();
		BeanUtil.copy(entity,visitInfoVo);
		List<Map> list = (List<Map>) JSONArray.parse(entity.getVisitDeptCodeArr());
		visitInfoVo.setVisitDeptCodeArr(list);
		return R.data(visitInfoVo);
	}


	@PostMapping("save")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "保存接口", notes = "传入dto")
	public R getData(@RequestBody VisitInfoDTO dto) {
		visitInfoService.saveData(dto);
		return R.success("保存成功");
	}

}
