package com.allcore.netty.controller;

import com.allcore.common.base.ZxhcController;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.netty.dto.GuidsDTO;
import com.allcore.netty.dto.WarningDTO;
import com.allcore.netty.dto.WarningSearchDTO;
import com.allcore.netty.service.WarningService;
import com.allcore.netty.vo.WarningVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author: Jiy
 * @Date: 2022-11-23 9:37
 */
@Slf4j
@RestController
@RequestMapping("/warning")
@Api(value = "告警消息接口", tags = "告警消息接口")
public class WarningController extends ZxhcController {
	@Autowired
	private WarningService warningService;

	@GetMapping("/page/list")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "告警消息列表", notes = "传入多条件查询")
	public R<IPage<WarningVO>> pageList(WarningSearchDTO warningSearchDTO, Query query) {
		IPage<WarningVO> warningVOIPage = warningService.pageList(warningSearchDTO, query);
		return R.data(warningVOIPage);
	}

	@PostMapping("/update")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "修改告警信息", notes = "传入warningDTO")
	public R update(@RequestBody WarningDTO warningDTO) {
		WarningVO warningVO = warningService.update(warningDTO);
		return R.data(warningVO);
	}

	@PostMapping("/remove")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "删除告警信息", notes = "传入guid集合多个逗号分开")
	public R remove(@RequestBody GuidsDTO dto) {
		warningService.remove(dto.getGuids());
		return R.success("删除成功");
	}

	@PostMapping("/export")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "批量导出", notes = "传入guid集合多个逗号分开")
	public void exportExcel(HttpServletResponse response, @RequestBody GuidsDTO dto) {
		try {
			warningService.export(response, dto.getGuids());
		} catch (Exception e) {
			log.error("消息管理列表导出异常-{}", e);
		}
	}

	@GetMapping("/total")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "统计未读消息数量", notes = "传入多条件查询")
	public R totalData(WarningSearchDTO warningSearchDTO) {
		return R.data(warningService.totalData(warningSearchDTO));
	}
}
