kind: Deployment
apiVersion: apps/v1
metadata:
  name: allcore-account
  #namespace: hbwrj-info
  labels:
    app: allcore-account
  annotations:
    deployment.kubernetes.io/revision: '3'
    kubesphere.io/creator: allcore
spec:
  replicas: 1
  selector:
    matchLabels:
      app: allcore-account
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: allcore-account
      annotations:
        kubesphere.io/restartedAt: '2023-03-06T04:13:44.219Z'
        logging.kubesphere.io/logsidecar-config: '{}'
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
        - name: home
          hostPath:
            path: /home
            type: ''
      containers:
        - name: allcore-account
          image: 'allcore.io/allcore/allcore-account:1.0.0.RELEASE'
          ports:
            - name: tcp-18066
              containerPort: 18066
              protocol: TCP
          env:
            - name: NACOS_CONFIG
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: NACOS_CONFIG
            - name: NACOS_DISCOVERY
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: NACOS_DISCOVERY
            - name: NACOS_NAMESPACE
              valueFrom:
                  configMapKeyRef:
                      name: common-conf
                      key: NACOS_NAMESPACE
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: SPRING_PROFILES_ACTIVE
            - name: TZ
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: TZ
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
            - name: home
              mountPath: /home
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirstWithHostNet
      hostNetwork: true
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
