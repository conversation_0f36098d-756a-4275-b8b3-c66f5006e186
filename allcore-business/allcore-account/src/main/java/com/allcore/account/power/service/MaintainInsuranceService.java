package com.allcore.account.power.service;


import com.allcore.account.power.dto.MaintainInsuranceDTO;
import com.allcore.account.power.entity.MaintainInsuranceEntity;
import com.allcore.account.power.vo.MaintainInsuranceExcelVO;
import com.allcore.account.power.vo.MaintainInsuranceVO;
import com.allcore.common.base.ZxhcService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface MaintainInsuranceService extends ZxhcService<MaintainInsuranceEntity> {

	/**
	 * 向批量添加保险
	 *
	 * @param productGuid
	 * @param insurances
	 * @return
	 */
	boolean addInsurancesToProduct(String productGuid, List<MaintainInsuranceDTO> insurances);

	/**
	 * 根据维保GUID删除保险
	 *
	 * @param productGuid
	 * @return
	 */
	boolean deleteInsurancesByProductGuid(String productGuid);

	/**
	 * 根据保险GUID删除保险
	 *
	 * @param insuranceGuids
	 * @return
	 */
	boolean deleteInsurancesByInsuranceGuids(List<String> insuranceGuids);

	/**
	 * 根据产品GUID获取保险VO列表
	 *
	 * @param productGuid
	 * @return
	 */
	List<MaintainInsuranceVO> getInsuranceVoListByProductGuid(String productGuid);

	List<MaintainInsuranceVO> getInstanceVoListByInsuranceGuids(List<String> insuranceGuids);

	/**
	 * 获取产品Guid:excelVoMap
	 *
	 * @param productGuids
	 * @return
	 */
	Map<String, List<MaintainInsuranceExcelVO>> getProductGuidInsuranceExcelVoMap(List<String> productGuids);
}
