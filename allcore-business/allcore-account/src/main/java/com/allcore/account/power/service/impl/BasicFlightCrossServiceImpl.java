package com.allcore.account.power.service.impl;

import com.allcore.account.power.dto.BasicFlightCrossDTO;
import com.allcore.account.power.entity.FlightCrossEntity;
import com.allcore.account.power.entity.TowerEntity;
import com.allcore.account.power.mapper.BasicFlightCrossMapper;
import com.allcore.account.power.mapper.TowerMapper;
import com.allcore.account.power.service.IBasicFlightCrossService;
import com.allcore.account.power.vo.BasicFlightCrossVO;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.enums.GridEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.mp.support.Query;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.dict.cache.DictBizCache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 飞行交跨表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-24
 */
@Service
public class BasicFlightCrossServiceImpl extends ZxhcServiceImpl<BasicFlightCrossMapper, FlightCrossEntity> implements IBasicFlightCrossService {

	@Resource
	private BasicFlightCrossMapper basicFlightCrossMapper;
	@Resource
	private TowerMapper towerMapper;

	@Override
	public R pageList(String depeCode, String voltageLevel, String lineGuid, Query query) {
		Page<BasicFlightCrossVO> page = new Page<>(query.getCurrent(), query.getSize());
		List<BasicFlightCrossVO> basicFlightCrossVOS = basicFlightCrossMapper.selectPageListByVoltageLevelLineGuid(page,depeCode, voltageLevel, lineGuid);
		basicFlightCrossVOS.stream().forEach(obj -> {
			//电压等级
			obj.setVoltageLevel(DictBizCache.getValue(BizDictEnum.VOLATE_LEVEL.getCode(),obj.getVoltageLevel()));
			obj.setCrossType(DictBizCache.getValue(BizDictEnum.FLIGHT_CROSS_TYPE.getCode(),obj.getCrossType()));

			obj.setProfessionalType(GridEnum.getNameByCode(obj.getProfessionalType()));
		});
		IPage<BasicFlightCrossVO> basicFlightCrossVOIPage = page.setRecords(basicFlightCrossVOS);
		return R.data(basicFlightCrossVOIPage);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public R add(BasicFlightCrossDTO dto) {
		FlightCrossEntity flightCrossEntity = new FlightCrossEntity();
		BeanUtils.copyProperties(dto, flightCrossEntity);
		flightCrossEntity.setFlightCrossGuid(CommonUtil.generateUuid()).setUnitGuid(AuthUtil.getDeptId());
		return R.data(this.save(flightCrossEntity));
	}
	@Transactional(rollbackFor = Exception.class)
	@Override
	public R delete(String flightCrossGuid) {
		if (StringUtils.isEmpty(flightCrossGuid)){
			return R.data(false);
		}
		boolean remove = this.remove(new LambdaQueryWrapper<FlightCrossEntity>().
			eq(FlightCrossEntity::getFlightCrossGuid,flightCrossGuid));
		return R.data(remove);
	}
	@Transactional(rollbackFor = Exception.class)
	@Override
	public R update(BasicFlightCrossDTO dto) {
		FlightCrossEntity flightCrossEntity = new FlightCrossEntity();
		BeanUtils.copyProperties(dto, flightCrossEntity);
		boolean update = this.update(flightCrossEntity, new LambdaQueryWrapper<FlightCrossEntity>()
			.eq(FlightCrossEntity::getFlightCrossGuid, flightCrossEntity.getFlightCrossGuid()));
		return R.data(update);
	}

	@Override
	public R detail(String flightCrossGuid) {
		FlightCrossEntity flightCrossEntity = basicFlightCrossMapper.selectOne(new LambdaQueryWrapper<FlightCrossEntity>()
			.eq(FlightCrossEntity::getFlightCrossGuid,flightCrossGuid));

		return R.data(flightCrossEntity);
	}

	//public void entyToVo(BasicFlightCross basicFlightCross){
	//
	//
	//}

	@Override
	public R<List<BasicFlightCrossVO>> queryFlightCrossInfoByLineGuids(List<String> lineGuids) {
		List<BasicFlightCrossVO> basicFlightCrossVOS = basicFlightCrossMapper.queryFlightCrossInfoByLineGuids(lineGuids);
		basicFlightCrossVOS.forEach(obj ->{
			obj.setCrossType(DictBizCache.getValue("010603038",obj.getCrossType()));
			TowerEntity tower = towerMapper.selectOne(new LambdaQueryWrapper<TowerEntity>().eq(TowerEntity::getTowerLineGuid, obj.getCrossSectionLineGuid()).eq(TowerEntity::getName, obj.getCrossLineArea()));
			if (tower!=null){
				obj.setTowerGuid(tower.getTowerGuid());
			}

		});
		return R.data(basicFlightCrossVOS);
	}
}
