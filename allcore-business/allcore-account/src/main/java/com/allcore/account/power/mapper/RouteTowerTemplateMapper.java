package com.allcore.account.power.mapper;

import com.allcore.account.power.entity.RouteTowerTemplateEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 杆塔关联模板表(RouteTowerTemplate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-08-31 17:32:47
 */
public interface RouteTowerTemplateMapper extends BaseMapper<RouteTowerTemplateEntity> {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    RouteTowerTemplateEntity queryById(String id);

    /**
     * 查询指定行数据
     *
     * @param page 查询条件
     * @param routeTowerTemplateEntity         分页对象
     * @return 对象列表
     */
    List<RouteTowerTemplateEntity> queryAllByLimit(IPage page, @Param("routeTowerTemplate") RouteTowerTemplateEntity routeTowerTemplateEntity);

    /**
     * 统计总行数
     *
     * @param routeTowerTemplateEntity 查询条件
     * @return 总行数
     */
    long count(RouteTowerTemplateEntity routeTowerTemplateEntity);


    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<RouteTowerTemplate> 实例对象列表
     * @return 影响行数
     */
    int insertAllRelation(@Param("entities") List<RouteTowerTemplateEntity> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<RouteTowerTemplate> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<RouteTowerTemplateEntity> entities);


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(String id);

    int deleteByTemplateGuid(String templateGuid);

	/**
	 * 物理删
	 * @param flyRecordList
	 * @return
	 */
	int deleteList(@Param("flyRecordList") List<String> flyRecordList);

	int deleteByDouble(@Param("flyRecordList") List<String> flyRecordList,@Param("templateGuid")String templateGuid);

}

