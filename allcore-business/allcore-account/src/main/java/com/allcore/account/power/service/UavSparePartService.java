package com.allcore.account.power.service;


import com.allcore.account.power.dto.*;
import com.allcore.account.power.entity.UavSparePartEntity;
import com.allcore.account.power.vo.InStockVO;
import com.allcore.account.power.vo.OutStockVO;
import com.allcore.account.power.vo.SparePartListVO;
import com.allcore.account.power.vo.UavSparePartVO;
import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: ldh
 * @date: 2022/9/14 19:10
 * @description: 无人机电池
 */
public interface UavSparePartService extends ZxhcService<UavSparePartEntity> {

	/**
	 * 添加信息
	 *
	 * @param infoDTO 信息dto
	 * @return {@link R}
	 */
	R addInfo(UavSparePartDTO infoDTO);

	/**
	 * 更新信息
	 *
	 * @param infoDTO 信息dto
	 * @return {@link R}
	 */
	R updateInfo(UavSparePartUpdateDTO infoDTO);

	/**
	 * 通过id获取信息
	 *
	 * @param id id
	 * @return {@link R}
	 */
	R getInfoByGuid(String sparePartGuid);

	/**
	 * 页面列表
	 *
	 * @param dto  dto
	 * @param page 页面
	 * @return {@link R}
	 */
	R pageList(UavSparePartPageDTO dto, IPage<UavSparePartVO> page);

	/**
	 * 德尔信息通过id
	 *
	 * @param id id
	 * @return {@link R}
	 */
	R delInfoById(String id);

	/**
	 * 现在有货
	 *
	 * @param infoDTO 信息dto
	 * @return {@link R}
	 */
	R inStock(InStockDTO infoDTO);

	/**
	 * 从股票
	 *
	 * @param infoDTO 信息dto
	 * @return {@link R}
	 */
	R outStock(OutStockDTO infoDTO);

	/**
	 * 在页面列表中
	 *
	 * @param id   id
	 * @param page 页面
	 * @return {@link R}<{@link IPage}<{@link InStockVO}>>
	 */
	R<IPage<InStockVO>> inPageList(String id, IPage<InStockVO> page);

	/**
	 * 从页面列表
	 *
	 * @param id   id
	 * @param page 页面
	 * @return {@link R}<{@link IPage}<{@link OutStockVO}>>
	 */
	R<IPage<OutStockVO>> outPageList(String id, IPage<OutStockVO> page);

	/**
	 * excel添加
	 *
	 * @param file 文件
	 * @return {@link R}
	 */
	R excelAdd(MultipartFile file);

	/**
	 * 无人机备件清单
	 *
	 * @param deptCode      部门代码
	 * @param uavBrand      无人机品牌
	 * @param modelGuid     模型guid
	 * @param sparePartType 备件类型
	 * @return {@link R}<{@link List}<{@link SparePartListVO}>>
	 */
	R<List<SparePartListVO>> uavSparePartList(String deptCode, String uavBrand, String modelGuid, String sparePartType, String sparePartModel);
}

