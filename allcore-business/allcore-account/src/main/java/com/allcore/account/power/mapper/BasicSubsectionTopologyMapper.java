package com.allcore.account.power.mapper;

import com.allcore.account.power.entity.SubsectionTopologyEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-09
 */
public interface BasicSubsectionTopologyMapper extends BaseMapper<SubsectionTopologyEntity> {

	List<SubsectionTopologyEntity> getListInfo();

	void insertBatch(@Param("list") List<SubsectionTopologyEntity> list );

}
