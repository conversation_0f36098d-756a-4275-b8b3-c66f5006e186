package com.allcore.account.power.service;


import com.allcore.account.power.dto.AreaManageDetailsDTO;
import com.allcore.account.power.dto.AreaManageEntityDTO;
import com.allcore.account.power.dto.AreaManageUpdateDTO;
import com.allcore.account.power.entity.AreaManageEntity;
import com.allcore.account.power.vo.AreaManageRelPersonEntityVO;
import com.allcore.account.power.vo.AreaManageRelUnitEntityVO;
import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.allcore.system.entity.Dept;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;


/**
 * <p>
 * 片区管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
public interface AreaManageService extends ZxhcService<AreaManageEntity> {

	/**
	 * 页面列表
	 *
	 * @param page 页面
	 * @return {@link R}
	 */
	R pageList(IPage<AreaManageEntityDTO> page);

	/**
	 * 查询区域单位详细信息
	 *
	 * @param areaManage 区域管理
	 * @param page       页面
	 * @return {@link R}
	 */
	R queryAreaUnitDetails(String areaManage, IPage<AreaManageRelUnitEntityVO> page);

	/**
	 * 查询区域人细节
	 *
	 * @param areaGuid 区域guid
	 * @param page     页面
	 * @return {@link R}
	 */
	R queryAreaPersonDetails(String areaGuid, IPage<AreaManageRelPersonEntityVO> page);

	/**
	 * 添加区域
	 *
	 * @param dto dto
	 * @return {@link R}
	 */
	R addArea(AreaManageDetailsDTO dto);

	/**
	 * 更新区域
	 *
	 * @param dto dto
	 * @return {@link R}
	 */
	R updateArea(AreaManageUpdateDTO dto);

	/**
	 * 删除区域
	 *
	 * @param id       id
	 * @param areaGuid 区域guid
	 * @return {@link R}
	 */
	R deleteArea(String id ,String areaGuid);

	/**
	 * 查询人通过部门id
	 *
	 * @param deptId 部门id
	 * @param name 登录名称
	 * @param realName 人员名称
	 * @return {@link R}
	 */
	R queryPersonByDeptId(String deptId,String name,String realName);

	/**
	 * 查询部门信息
	 *
	 * @param parentId 部门父id
	 * @param deptName 单位名称
	 * @return {@link R}
	 */
	R queryDept(String parentId, String deptName);


	/**
	 * 根据用户查询片区单位信息
	 *
	 * @param userGuid 部门父id
	 * @return {@link R}
	 */
	R<List<Dept>> getAreaDeptByUser(String userGuid);


}
