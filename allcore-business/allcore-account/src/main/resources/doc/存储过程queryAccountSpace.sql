CREATE DEFINER=`root`@`%` PROCEDURE `queryAccountSpace`(IN `modelCode` varchar(50),IN `parentCode` varchar(50))
BEGIN
  SET @EE = '';

SELECT
    @EE := CONCAT( @EE, 'MAX(if(space_basic_code=  \'', model_basic_code, '\',space_basic_value,"")) as \'', model_basic_code, '\',' ) AS aa
FROM
    (
    SELECT
    A.model_basic_code,
    A.model_basic_name
    FROM
    account_model_basic_info A
    LEFT JOIN account_model_model_basic B ON A.model_basic_guid = B.model_basic_guid
    LEFT JOIN account_model_info C ON B.model_guid = C.model_guid
    WHERE
    C.model_code = modelCode
    ORDER BY
    A.sort
    ) A;
IF parentCode <> '' THEN
SET @querySql = CONCAT( 'select ', @EE, ' space_guid from (
	SELECT
	B.space_guid,
	B.space_basic_value,
	B.space_basic_code
	FROM
	account_space A
	LEFT JOIN account_space_basic B ON A.space_guid = B.space_guid
	WHERE
	B.is_deleted = \'0\' AND
	A.model_code = \'', modelCode, '\'
	AND A.space_parent_guid = \'', parentCode, '\'
	GROUP BY
	B.space_guid,
	B.space_basic_code
) a GROUP BY a.space_guid' );
ELSE
SET @querySql = CONCAT( 'select ', @EE, ' a.space_guid from (
	SELECT
	B.space_guid,
	B.space_basic_value,
	B.space_basic_code,
	B.type
	FROM
	account_space A
	LEFT JOIN account_space_basic B ON A.space_guid = B.space_guid
	WHERE
	B.is_deleted = \'0\' AND
	A.model_code = \'', modelCode, '\'
	GROUP BY
	B.space_guid,
	B.space_basic_code
) a GROUP BY a.space_guid' );
END IF;

PREPARE stmt
    FROM
    @querySql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

END
