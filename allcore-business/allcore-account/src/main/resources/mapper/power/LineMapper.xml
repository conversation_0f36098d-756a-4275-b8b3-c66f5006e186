<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.account.power.mapper.LineMapper">


    <select id="getLinePage" resultType="com.allcore.account.power.vo.LineInfoVo">
        select a.line_guid lineGuid,
        a.voltage_level voltageLevel,a.pm_code pmCode,
        a.name lineName,a.length as totalLength,a.maint_group maintGroup,a.maint_org maintOrg
        <!--t.work_area_guid workAreaGUID-->
        from account_line_ledger a
        <!--LEFT JOIN account_tower_ledger t on a.line_guid = t.tower_line_guid-->
        <where>
            1 = 1
            <if test=" major != null and major != '' ">
                and a.professional_type = #{major}
            </if>
            <if test=" unitCode != null and unitCode != '' ">
                and a.dept_code like concat('%',#{unitCode},'%')
            </if>
            <if test=" voltageLevel != null and voltageLevel != '' ">
                and a.voltage_level = #{voltageLevel}
            </if>
            <if test=" lineName != null and lineName!= '' ">
                and a.name like concat('%',#{lineName},'%')
            </if>
            <!--GROUP BY t.tower_line_guid-->
        </where>

    </select>
    <select id="getRouteCount" resultType="java.lang.Integer" parameterType="java.lang.String">
        select count(r.route_info_guid)
        from account_route_line_info r
        where r.line_guid = #{lineGUid}
    </select>
    <select id="getTowerCountByPid" resultType="java.lang.Integer">
        select count(id)
        from account_tower_ledger
        where tower_line_guid = #{lineGuid}
    </select>
    <select id="getTowerInfoEx" resultType="com.allcore.account.power.excel.TowerEntityExcel">
        select b.id, b.tower_guid, b.tower_line_guid, b.name, b.geo_position, b.city, b.line, b.maint_org,
        b.equipment_owner, b.voltage_level, b.psr_state, b.start_time, b.stop_time, b.is_rural, b.branch_feeder,
        b.physicalpole_type, b.switch_segment, b.wire, b.is_changephase, b.pole_num, b.norminal_height, b.superior_line,
        b.is_branch, b.tower_longitude, b.tower_latitude, b.tower_sort, b.location, b.pole_nature,
        b.model, b.pole_material, b.loop_number, b.span, b.regular_distance, b.strain_length, b.is_terminal,
        b.head_height, b.altitude, b.pole_high, b.is_turn, b.turn_direction, b.turn_degrees, b.is_same_pole,
        b.regionalism, b.geology, b.terrain, b.work_area_guid, b.maint_group, b.asset_equipment_code,
        b.resource_equipment_code, b.is_airworthy_area, b.airworthy_area_from, b.fly_ban_guid, b.real_longitude,
        b.real_latitude, b.is_main_tower, b.tower_branch_name, b.psr_id, b.ast_id, b.pm_code, b.line_position,
        b.wire_arrangement, b.phase_pole, b.wait_maintain_company, b.professional_type, b.is_old, b.create_user,
        b.create_time, b.update_user, b.update_time, b.dept_code, b.create_dept, b.is_deleted
        from account_line_ledger a
        inner join account_tower_ledger b on a.line_guid = b.tower_line_guid
        <where>
            a.professional_type = #{major}
            <if test=" unitCode != null and unitCode != '' ">
                and a.dept_code like concat('%',#{unitCode},'%')
            </if>
            <if test=" voltageLevel != null and voltageLevel != '' ">
                and a.voltage_level = #{voltageLevel}
            </if>
            <if test=" lineName != null and lineName!= '' ">
                and a.name like concat('%',#{lineName},'%')
            </if>
        </where>
        <!--<if test=" unitCode != null and unitCode != '' ">
            UNION
            select b.*
            from account_wait_maintain_ledger wait
            left join account_line_ledger a  ON wait.line_guid = a.line_guid
            left join account_tower_ledger b on a.line_guid = b.tower_line_guid
            WHERE wait.dept_code like concat(#{unitCode},'%')
            a.professional_type = #{major}
            <if test=" voltageLevel != null and voltageLevel != '' ">
                and a.voltage_level = #{voltageLevel}
            </if>
            <if test=" lineName != null and lineName!= '' ">
                and a.name like concat('%',#{lineName},'%')
            </if>
        </if>-->


    </select>
    <select id="getGuidsByDeptCodeAndQuery" resultType="com.allcore.account.power.vo.TowerTemplateVO">
        SELECT
        t.tower_guid as towerGuid,
        l.dept_code as unitCode,
        l.line_guid as lineGuid,
        l.voltage_level as voltageLevel,
        l.`name` AS lineName,
        l.professional_type as major,
        t.`name` as towerNo,
        t.pole_nature as towerType,
        t.loop_number as loopNumber,
        tem.tower_guid towerGuidMemo,
        tem.template_guid templateGuid,
        tem.tower_template_guid towerTemplateGuid,
        ler.template_name templateName
        FROM
        account_line_ledger l
        LEFT JOIN account_tower_ledger t ON l.line_guid = t.tower_line_guid
        LEFT JOIN account_route_tower_template tem ON t.tower_guid = tem.tower_guid
        LEFT JOIN account_route_learn_template ler ON tem.template_guid = ler.template_guid AND ler.is_deleted != 1
        <where>
            t.tower_line_guid is not null
            <if test=" deptCode != null and deptCode != '' ">
                and l.dept_code like concat(#{deptCode},'%')
            </if>
            <if test=" lineName != null and lineName != '' ">
                and l.`name` like concat('%',#{lineName},'%')
            </if>
            <if test=" voltageLevel != null and voltageLevel != '' ">
                and l.voltage_level = #{voltageLevel}
            </if>
            <if test=" towerType != null and towerType != '' ">
                and t.pole_nature = #{towerType}
            </if>
            <if test=" loopNumber != null and loopNumber != '' ">
                and t.loop_number = #{loopNumber}
            </if>
            <if test=" major != null and major != '' ">
                and l.professional_type = #{major}
            </if>
            <if test=" templateName != null and templateName != '' ">
                and ler.template_name like concat('%',#{templateName},'%')
            </if>
        </where>
        ORDER BY lineName asc, towerNo asc
        limit #{start},#{end}
    </select>
    <select id="getGuidsByDeptCodeAndQueryCount" resultType="java.lang.Integer">
        SELECT
        count(t.tower_guid)
        FROM
        account_line_ledger l
        LEFT JOIN account_tower_ledger t ON l.line_guid = t.tower_line_guid
        LEFT JOIN account_route_tower_template tem ON t.tower_guid = tem.tower_guid
        LEFT JOIN account_route_learn_template ler ON tem.template_guid = ler.template_guid AND ler.is_deleted !=1
        <where>
            t.tower_line_guid is not null
            <if test=" deptCode != null and deptCode != '' ">
                and l.dept_code like concat(#{deptCode},'%')
            </if>
            <if test=" lineName != null and lineName != '' ">
                and l.`name` like concat('%',#{lineName},'%')
            </if>
            <if test=" voltageLevel != null and voltageLevel != '' ">
                and l.voltage_level = #{voltageLevel}
            </if>
            <if test=" towerType != null and towerType != '' ">
                and t.pole_nature = #{towerType}
            </if>
            <if test=" loopNumber != null and loopNumber != '' ">
                and t.loop_number = #{loopNumber}
            </if>
            <if test=" major != null and major != '' ">
                and l.professional_type = #{major}
            </if>
            <if test=" templateName != null and templateName != '' ">
                and ler.template_name like concat('%',#{templateName},'%')
            </if>
        </where>

    </select>
    <select id="getTowerGuidsHaveTemplate" resultType="java.lang.String">
        SELECT
        t.tower_guid as towerGuid
        FROM
        account_line_ledger l
        LEFT JOIN account_tower_ledger t ON l.line_guid = t.tower_line_guid
        LEFT JOIN account_route_tower_template tem ON t.tower_guid = tem.tower_guid
        <where>
            t.tower_line_guid is not null AND tem.tower_template_guid IS NOT NULL
            and tem.is_deleted = 0
            <if test=" voltageLevel != null and voltageLevel != '' ">
                and l.voltage_level = #{voltageLevel}
            </if>
            <if test=" towerType != null and towerType != '' ">
                and t.pole_nature = #{towerType}
            </if>
            <if test=" loopNumber != null and loopNumber != '' ">
                and t.loop_number = #{loopNumber}
            </if>
        </where>
    </select>
    <select id="getTowerGuids" resultType="java.lang.String">
        SELECT
        t.tower_guid as towerGuid
        FROM
        account_line_ledger l
        LEFT JOIN account_tower_ledger t ON l.line_guid = t.tower_line_guid
        <where>
            t.tower_line_guid is not null
            <if test=" voltageLevel != null and voltageLevel != '' ">
                and l.voltage_level = #{voltageLevel}
            </if>
            <if test=" towerType != null and towerType != '' ">
                and t.pole_nature = #{towerType}
            </if>
            <if test=" loopNumber != null and loopNumber != '' ">
                and t.loop_number = #{loopNumber}
            </if>
        </where>
    </select>
    <select id="getLinePageWithMaintain" resultType="com.allcore.account.power.vo.LineInfoVo">
        select a.line_guid lineGuid,
        a.voltage_level voltageLevel,
        a.name lineName,a.length as totalLength,a.maint_group maintGroup,a.maint_org maintOrg
        from account_line_ledger a
        <where>
            <if test=" major != null and major != '' ">
                and a.professional_type = #{major}
            </if>
            <if test=" major == null or major == '' ">
                and a.professional_type in('tms','dms')
            </if>
            <if test=" unitCode != null and unitCode != '' ">
                and a.dept_code like concat('%',#{unitCode},'%')
            </if>
            <if test=" voltageLevel != null and voltageLevel != '' ">
                and a.voltage_level = #{voltageLevel}
            </if>
            <if test=" lineName != null and lineName!= '' ">
                and a.name like concat('%',#{lineName},'%')
            </if>
        </where>
        <if test=" unitCode != null and unitCode != '' ">
            UNION
            select a.line_guid lineGuid,
            a.voltage_level voltageLevel,
            a.name lineName,a.length as totalLength,a.maint_group maintGroup,a.maint_org maintOrg
            from account_wait_maintain_ledger maint
            left join account_line_ledger a ON maint.line_guid = a.line_guid
            WHERE maint.dept_code like concat(#{unitCode},'%')
            and a.professional_type in('tms','dms')
<!--            <if test=" major != null and major != '' ">-->
<!--                and a.professional_type = #{major}-->
<!--            </if>-->
            <if test=" voltageLevel != null and voltageLevel != '' ">
                and a.voltage_level = #{voltageLevel}
            </if>
            <if test=" lineName != null and lineName!= '' ">
                and a.name like concat('%',#{lineName},'%')
            </if>
        </if>
    </select>
    <select id="getLineVoByDeptCode" resultType="com.allcore.account.power.vo.LineInVO">
        select line_guid lineGuid,name lineName from account_line_ledger where dept_code like concat('%',#{deptCode},'%')
    </select>
    <select id="getLinesByDeptCode" resultType="com.allcore.account.power.entity.LineEntity">
        SELECT
            id, line_guid, name, run_dev_name, line_tower_number, voltage_level, city, maint_org, maint_group, psr_state, start_station, end_station, start_time, stop_time, start_type, end_type, start_position, end_position, start_switch, end_switch, erection_method, overhead_method, cable_method, is_rural, line_nature, main_line, superior_line, length, overhead_length, cable_length, regionalism, supply_area, feeder, cross_region_type, is_grounded, is_standardized, pm_code, psr_id, ast_id, professional_type, create_user, create_time, update_user, update_time, dept_code, create_dept, is_deleted
        FROM
            `account_line_ledger`
        WHERE
            FIND_IN_SET(
                #{deptCode},
                dept_code)
    </select>
    <select id="queryLineGuidByName" resultType="java.lang.String" parameterType="java.lang.String">
        select line_guid from account_line_ledger where name = #{lineName}
    </select>
    <select id="deviceInfoList" resultType="com.allcore.account.power.vo.DeviceVO">
        SELECT
	    l.NAME AS groupName,
	    l.line_guid AS groupGuid,
	    0 AS groupType,
	    t.real_latitude as la ,
	    t.real_longitude as lo,
	    t.real_altitude as al,
	    t.tower_guid AS deviceGuid,
	    t.tower_sort AS deviceNumber,
	    t.`name` AS deviceName,
	    t.voltage_level AS voltageLevel,
	    t.pm_code AS pmsCode
        FROM
	    account_line_ledger l
	    INNER JOIN account_tower_ledger t ON l.line_guid = t.tower_line_guid
        WHERE
	    l.line_guid = #{groupGuid}

    </select>

    <select id="groupInfoList" resultType="com.allcore.account.power.vo.DeviceTowerVO">
        select nums.*,GROUP_CONCAT(nums.tower_guid) AS towerGuids from
        (
        SELECT
        l.NAME AS groupName,
        l.line_guid AS groupGuid,
        case when l.professional_type = 'tms' then 0 else 1 end as deviceType,
        t.tower_guid
        FROM
        account_line_ledger l
        INNER JOIN account_tower_ledger t ON l.line_guid = t.tower_line_guid
        WHERE
        l.dept_code like concat(#{deptCode},'%')
        <if test="lineName != null">
            and l.`name` like concat('%',#{lineName},'%')
        </if>
        union
        SELECT
        l.NAME AS groupName,
        l.line_guid AS groupGuid,
        case when l.professional_type = 'tms' then 0 else 1 end as deviceType,
        t.tower_guid
        FROM
        account_wait_maintain_ledger waitl
        INNER JOIN account_line_ledger l on waitl.line_guid = l.line_guid
        INNER JOIN account_tower_ledger t ON l.line_guid = t.tower_line_guid
        WHERE
        waitl.dept_code like concat(#{deptCode},'%')
        <if test="lineName != null">
            and l.`name` like concat('%',#{lineName},'%')
        </if>
        ) nums
        GROUP BY nums.groupGuid
    </select>

    <select id="getLinesByDeptCodeAndVoltageLevel" resultType="com.allcore.account.power.entity.LineEntity">
        SELECT
            id, line_guid, name, run_dev_name, line_tower_number, voltage_level, city, maint_org, maint_group, psr_state,
            start_station, end_station, start_time, stop_time, start_type, end_type, start_position, end_position,
            start_switch, end_switch, erection_method, overhead_method, cable_method, is_rural, line_nature, main_line,
            superior_line, length, overhead_length, cable_length, regionalism, supply_area, feeder, cross_region_type,
            is_grounded, is_standardized, pm_code, psr_id, ast_id, professional_type, create_user, create_time,
            update_user, update_time, dept_code, create_dept, is_deleted
        FROM
            `account_line_ledger`
        WHERE
            FIND_IN_SET(
                #{deptCode},
                dept_code) and voltage_level = #{voltageLecvel}
    </select>
    <select id="getWaitLineInfo" resultType="com.allcore.account.power.entity.LineEntity">
        SELECT line.line_guid,line.voltage_level, "1" as pmCode,line.name,line.voltage_level FROM account_wait_maintain_ledger maint

        INNER JOIN account_line_ledger line on maint.line_guid = line.line_guid

        WHERE maint.dept_code like concat(#{deptCode},'%')
       <if test="voltageLecvel != null">
           and line.voltage_level = #{voltageLecvel}
       </if>


    </select>

</mapper>
