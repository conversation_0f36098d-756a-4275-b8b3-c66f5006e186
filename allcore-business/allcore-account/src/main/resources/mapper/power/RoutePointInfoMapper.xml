<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.account.power.mapper.RoutePointInfoMapper">

    <resultMap type="com.allcore.account.power.entity.RoutePointInfoEntity" id="RoutePointInfoMap">
        <result property="id" column="id" jdbcType="VARCHAR"/>
        <result property="routeInfoGuid" column="route_info_guid" jdbcType="VARCHAR"/>
        <result property="pointNumber" column="point_number" jdbcType="VARCHAR"/>
        <result property="pointIndex" column="point_index" jdbcType="VARCHAR"/>
        <result property="longitude" column="longitude" jdbcType="VARCHAR"/>
        <result property="latitude" column="latitude" jdbcType="VARCHAR"/>
        <result property="height" column="height" jdbcType="VARCHAR"/>
        <result property="deviationAngle" column="deviation_angle" jdbcType="VARCHAR"/>
        <result property="actionPreTime" column="action_pre_time" jdbcType="VARCHAR"/>
        <result property="flightRate" column="flight_rate" jdbcType="VARCHAR"/>
        <result property="actionsCount" column="actions_count" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="createDept" column="create_dept" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDeleted" column="is_deleted" jdbcType="INTEGER"/>
        <result property="deptCode" column="dept_code" jdbcType="VARCHAR"/>
    </resultMap>


    <delete id="deleteList" parameterType="java.util.List">
        delete from account_route_point_info
        where point_guid in
        <foreach collection="appFlyRecordList" item="it" open="(" close=")" separator=",">
            #{it}
        </foreach>
    </delete>

</mapper>

