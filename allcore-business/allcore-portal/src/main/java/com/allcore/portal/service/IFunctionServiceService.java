package com.allcore.portal.service;

import com.allcore.portal.entity.FunctionService;
import com.allcore.portal.vo.FunctionServiceVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface IFunctionServiceService extends IService<FunctionService> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param functionService
	 * @return
	 */
	IPage<FunctionServiceVO> selectFunctionServicePage(IPage<FunctionServiceVO> page, FunctionServiceVO functionService);

}
