package com.allcore.portal.mapper;

import com.allcore.portal.entity.FunctionService;
import com.allcore.portal.vo.FunctionServiceVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
public interface FunctionServiceMapper extends BaseMapper<FunctionService> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param functionService
	 * @return
	 */
	List<FunctionServiceVO> selectFunctionServicePage(IPage page, FunctionServiceVO functionService);

}
