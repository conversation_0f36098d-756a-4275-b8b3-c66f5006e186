package com.allcore.portal.controller;

import com.allcore.core.boot.ctrl.AllcoreController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.Func;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.allcore.portal.entity.FunctionService;
import com.allcore.portal.vo.FunctionServiceVO;
import com.allcore.portal.wrapper.FunctionServiceWrapper;
import com.allcore.portal.service.IFunctionServiceService;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/functionservice")
@Api(value = "", tags = "接口")
public class FunctionServiceController extends AllcoreController {

	private final IFunctionServiceService functionServiceService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入functionService")
	public R<FunctionServiceVO> detail(FunctionService functionService) {
		FunctionService detail = functionServiceService.getOne(Condition.getQueryWrapper(functionService));
		return R.data(FunctionServiceWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入functionService")
	public R<IPage<FunctionServiceVO>> list(FunctionService functionService, Query query) {
		IPage<FunctionService> pages = functionServiceService.page(Condition.getPage(query), Condition.getQueryWrapper(functionService));
		return R.data(FunctionServiceWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入functionService")
	public R<IPage<FunctionServiceVO>> page(FunctionServiceVO functionService, Query query) {
		IPage<FunctionServiceVO> pages = functionServiceService.selectFunctionServicePage(Condition.getPage(query), functionService);
		return R.data(pages);
	}

	/**
	 * 新增 
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入functionService")
	public R save(@Valid @RequestBody FunctionService functionService) {
		return R.status(functionServiceService.save(functionService));
	}

	/**
	 * 修改 
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入functionService")
	public R update(@Valid @RequestBody FunctionService functionService) {
		return R.status(functionServiceService.updateById(functionService));
	}

	/**
	 * 新增或修改 
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入functionService")
	public R submit(@Valid @RequestBody FunctionService functionService) {
		return R.status(functionServiceService.saveOrUpdate(functionService));
	}

	
	/**
	 * 删除 
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(functionServiceService.removeBatchByIds(Func.toStrList(ids)));
	}

	
}
