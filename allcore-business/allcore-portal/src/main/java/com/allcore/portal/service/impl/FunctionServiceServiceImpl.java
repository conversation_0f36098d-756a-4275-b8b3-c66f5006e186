package com.allcore.portal.service.impl;

import com.allcore.portal.entity.FunctionService;
import com.allcore.portal.mapper.FunctionServiceMapper;
import com.allcore.portal.service.IFunctionServiceService;
import com.allcore.portal.vo.FunctionServiceVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Service
public class FunctionServiceServiceImpl extends ServiceImpl<FunctionServiceMapper, FunctionService> implements IFunctionServiceService {

	@Override
	public IPage<FunctionServiceVO> selectFunctionServicePage(IPage<FunctionServiceVO> page, FunctionServiceVO functionService) {
		return page.setRecords(baseMapper.selectFunctionServicePage(page, functionService));
	}

}
