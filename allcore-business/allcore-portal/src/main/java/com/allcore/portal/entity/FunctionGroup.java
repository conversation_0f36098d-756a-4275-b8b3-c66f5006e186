package com.allcore.portal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Data
@TableName("portal_function_group")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FunctionGroup对象", description = "FunctionGroup对象")
public class FunctionGroup extends Model<FunctionGroup> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty("主键id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 分组名
     */
    @ApiModelProperty(value = "分组名")
    private String groupName;
    /**
     * 所属项目id
     */
    @ApiModelProperty(value = "所属项目id")
    private Long projectId;
    /**
     * 分组排序
     */
    @ApiModelProperty(value = "分组排序")
    private Integer sort;


}
