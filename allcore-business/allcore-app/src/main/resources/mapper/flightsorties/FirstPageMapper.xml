<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.app.code.solve.mapper.FirstPageMapper">


    <select id="getAreaName" resultType="com.allcore.main.code.source.entity.PvArea">
        select * from main_pv_area where is_deleted = 0 and dept_code = #{deptCode} order by device_name limit 1
    </select>
    <select id="getStillDefectTagByDeviceId"
            resultType="com.allcore.main.code.inspection.entity.InspectionPictureTagging">
        SELECT
        a.id,
        a.pv_component_id,
        a.defect_level,
        a.defect_description,
        a.eliminate_status,
        b.remove_task_status
        FROM
        main_inspection_picture_tagging a
        inner join main_remove_picture_tagging b on a.id = b.inspection_picture_tagging_id
        WHERE
        a.is_deleted = 0 and b.is_deleted = 0
        and a.pv_component_id is not null
        and a.defect_type = 'defect'
        and a.eliminate_status != 'not_push'
        and b.remove_user = #{removeUser}
        and a.device_id = #{pvAreaId}
        <if test="defectDescription != null and defectDescription!=''">
            and a.defect_description like concat(#{defectDescription}, '%')
        </if>
    </select>

    <select id="getStillDefectTagByDept"
            resultType="com.allcore.main.code.inspection.entity.InspectionPictureTagging">
        SELECT
            a.defect_description,
            a.eliminate_status
        FROM
            main_inspection_picture_tagging a
                INNER JOIN main_remove_picture_tagging c ON a.id = c.inspection_picture_tagging_id
        WHERE
            a.is_deleted = 0
          AND c.is_deleted = 0
          AND c.remove_task_status IN ( 'doing', 'completed' )
          AND a.device_type = 'PV'
          AND a.defect_type = 'defect'
          AND c.remove_user = #{removeUser}
    </select>



    <select id="select" resultType="com.allcore.main.code.source.vo.PvAreaVO">
        select * from main_pv_area where is_deleted = 0 and dept_code = #{deptCode}
        order by device_name
    </select>
    <select id="removeTaskList" resultType="com.allcore.main.code.solve.vo.RemoveTaskAppVO">
        SELECT
            a.id,
            a.remove_task_name,
            a.create_time,
            b.update_time,
            a.remove_task_status,
            b.create_time AS sendTime,
            b.sendDefectNum,
            b.tagCompletedCnt
        FROM
            main_remove_task a
                INNER JOIN ( SELECT
                                 remove_task_id,
                                 max( create_time ) AS create_time,
                                 max( remove_time ) AS update_time,
                                 count( 1 ) AS sendDefectNum,
                                 count(case when remove_task_status = 'completed' then 1 end) as tagCompletedCnt
                             FROM main_remove_picture_tagging
                             WHERE remove_user = #{removeUser} and is_deleted = 0
                             GROUP BY remove_task_id )
                    b ON a.id = b.remove_task_id
            where a.is_deleted = 0
                <if test="removeTaskStatus != null and removeTaskStatus != ''">
                    and a.remove_task_status = #{removeTaskStatus}
                </if>
              and a.remove_task_status in ('doing','completed')
                <if test="timeType != null and timeType != '' and timeType != 'month'">
                    and b.create_time >= #{startTime} and  #{endTime} >=  b.create_time
                </if>
            order by a.create_time desc
    </select>
    <select id="getInspectionTask" resultType="com.allcore.main.code.inspection.entity.InspectionTask">
        select * from main_inspection_task where id = (select inspection_task_id from main_remove_task where id = #{removeTaskId})
    </select>
    <select id="defectList" resultType="com.allcore.main.code.solve.vo.RemoveTaskDefectAppVO">
        select
               b.remove_task_no,
               b.remove_task_status,
               c.id as  inspectionPictureTaggingId,
               c.pv_component_id,
               c.defect_description,
               c.defect_level,
               d.device_name,
               e.device_name as pvAreaName,
               b.send_time,
               b.remove_time as processedTime,
               b.update_time
               from main_remove_picture_tagging b
                 inner join main_inspection_picture_tagging c on b.inspection_picture_tagging_id = c.id
                 inner join main_pv_components d on c.pv_component_id = d.id
                 inner join main_pv_area e on d.pv_area_id = e.id
        where
         b.is_deleted = 0 and c.is_deleted = 0 and b.remove_user = #{removeUser}
        and c.device_type = 'PV'
        <if test="removeTaskStatus != null and removeTaskStatus != ''">
            and b.remove_task_status = #{removeTaskStatus}
        </if>
        and b.remove_task_status in ('doing','completed')
        <if test="timeType != null and timeType != ''">
            and b.send_time >= #{startTime} and  #{endTime} >=  b.send_time
        </if>
        order by b.remove_task_status desc,b.send_time desc

    </select>
</mapper>
