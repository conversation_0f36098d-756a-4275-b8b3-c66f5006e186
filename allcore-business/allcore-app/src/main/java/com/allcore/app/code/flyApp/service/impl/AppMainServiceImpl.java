package com.allcore.app.code.flyApp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.allcore.app.code.flightsorties.dto.*;
import com.allcore.app.code.flightsorties.entity.*;
import com.allcore.app.code.flightsorties.service.IFlyFlightSortiesService;
import com.allcore.app.code.flightsorties.vo.*;
import com.allcore.app.code.flyApp.service.IAppMainService;
import com.allcore.app.code.flyApp.service.IFlyPtzService;
import com.allcore.app.code.flyApp.service.IFlyRealInfoService;
import com.allcore.app.code.tool.MqTool;
import com.allcore.app.code.util.SpringUtils;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.constant.MessageConstant;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.api.ResultCode;
import com.allcore.core.tool.utils.AESCrypt;
import com.allcore.core.tool.utils.ObjectUtil;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.inspection.dto.AppInspectionPictureDTO;
import com.allcore.main.code.inspection.dto.BindPicDTO;
import com.allcore.main.code.inspection.dto.FolderUpLoadDTO;
import com.allcore.main.code.inspection.dto.InspectionPictureSaveDTO;
import com.allcore.main.code.inspection.entity.InspectionTask;
import com.allcore.main.code.inspection.feign.MainForAppClient;
import com.allcore.main.code.inspection.vo.AppRouteResponseVO;
import com.allcore.main.code.source.vo.AppLedgerDetailVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.security.GeneralSecurityException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @description: APP对接业务服务层
 * @author: wp
 * @date: 2022/3/14
 */
@Service
@Slf4j
@Validated
@RefreshScope
public class AppMainServiceImpl implements IAppMainService {


    /**
     * 是否接收app回传的云台信息，默认不接收
     */
    @Value("${is.receive.gimbal:false}")
    private boolean isReceiveGimbal;
    @Value("${plane.media.pushUrl}")
    private String pushUrl;
    @Value("${plane.media.playUrl}")
    private String playUrl;

    @Resource
    private MainForAppClient iFlyTaskClient;


    @Resource
    private AllcoreRedis redis;

    @Resource
    private IFlyRealInfoService flyRealInfoService;

    @Resource
    private IFlyPtzService flyPtzService;

    @Resource
    private IFlyFlightSortiesService flyFlightSortiesService;
    private static UavInfoNow now = new UavInfoNow();
    @Resource
    private IOssClient ossClient;
    private MqTool mqTool = SpringUtils.getBean(MqTool.class);

//    @Resource
//    private ICommonFileClient commonFileClient;
//
//    @Resource
//    private IDeviceForMainClient deviceForMainClient;
//
//    @Resource
//    private IDefectClient defectClient;

    @Override
    public R pushMediaUrl(String param) {
        log.info("app获取视频流地址：{}", param);
        try {
            if (StringUtils.isBlank(param)) {
                return R.data(ResultCode.FAILURE.getCode(), StringPool.EMPTY, MessageConstant.PARAM_ERR);
            }
            // 解析 监控模块编号
            param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            if (StringUtils.isBlank(param)) {
                return R.data(ResultCode.FAILURE.getCode(), StringPool.EMPTY, MessageConstant.DECRYPT_PARAM_ERR);
            }
            //转换JSONObject 取出 equipmentNo
            JSONObject equipmentNoObj = JSON.parseObject(param);
            if (null == equipmentNoObj) {
                return R.data(ResultCode.FAILURE.getCode(), StringPool.EMPTY, MessageConstant.DECRYPT_PARAM_ERR);
            }
            log.info("app获取视频流地址接口获取解密后数据串：{}", param);
            String equipmentCode = equipmentNoObj.getString(CommonConstant.EQUIPMENT_NO);
            // 根据监控模块编号 查询飞机信息
            /*R<Map<String,String>> data = FeignDataUtil.format(iFlyTaskClient.getUavInformationForApp(equipmentCode));
            if(data.getCode()== ResultCode.SUCCESS.getCode() && data.getData() != null){*/
            // 拼接推流地址
            AppPlaneMediaUrlResponseVO responseDTO = new AppPlaneMediaUrlResponseVO();
            responseDTO.setId(StringPool.EMPTY);
            responseDTO.setOrderNo(StringPool.EMPTY);
            responseDTO.setKeyName(StringPool.EMPTY);
            responseDTO.setValue(pushUrl + StringPool.SLASH + equipmentCode + playUrl);
            String aesStr = AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, JSON.toJSONString(responseDTO));
            log.info("app获取视频流地址接口响应：{}", aesStr);
            return R.data(aesStr);
            //}
        } catch (Exception e) {
            log.error("app获取视频流地址接口异常： {}", e.toString());
            e.printStackTrace();
        }
        return R.data(ResultCode.FAILURE.getCode(), StringPool.EMPTY, MessageConstant.SELECT_FAIL);
    }

    @Override
    public R receiveUavDataNew(boolean isOwn, String param) {
        StopWatch stopWatch = new StopWatch("appReceiveUavDataNew");
        stopWatch.start();
        // 是否是因诺风机回传
        boolean isYinNuoFan = false;
        try {
            if (StringUtils.isBlank(param)) {
                return R.data(ResultCode.FAILURE.getCode(), "", "param为空");
            }
            if (isOwn) {
                log.info("移动端在飞无人机实时信息回传....");
                param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            } else {
                log.info("第三方移动端在飞无人机实时信息回传....");
                isYinNuoFan = true;
                param = AESCrypt.decrypt(CommonConstant.THIRD_PARTY_ASE_KEY, param);
            }

            AppAircraftStateRequestParamDTO appAircraft = JSONObject.parseObject(param, AppAircraftStateRequestParamDTO.class);
            log.info("在飞无人机状态信息集合接口获取解密后数据串：{}", JSON.toJSONString(appAircraft));
            if (appAircraft == null) {
                return R.data(ResultCode.FAILURE.getCode(), "", "解密后参数为空");
            }
            //先判断当前飞行的工单是否有效(工单存在)
            AppTaskStateParamDTO taskState = appAircraft.getTask();

            if (taskState == null) {
                return R.data(ResultCode.FAILURE.getCode(), "", "飞行任务信息为空");
            }
            if (StringUtil.isBlank(taskState.getFlyGuid())) {
                return R.data(ResultCode.FAILURE.getCode(), "", "飞行架次为空");
            }

            if (isYinNuoFan) {
                // 因诺风机
                taskState.setDeviceType(33);
            }
            //获取工单guid
            String inspectGuid = taskState.getInspectGuid();
            InspectionTask taskInfo;
            if (StringUtil.isBlank(inspectGuid)) {
                return R.data(ResultCode.FAILURE.getCode(), "", "工单参数为空");
            } else {
                R<InspectionTask> flyTaskInfoByGuid = iFlyTaskClient.getOrderByGuid(inspectGuid);
                if (!flyTaskInfoByGuid.isSuccess() || null == flyTaskInfoByGuid.getData()) {
                    return R.data(ResultCode.FAILURE.getCode(), "", "工单不存在");
                }
                taskInfo = flyTaskInfoByGuid.getData();
                // 推送前端显示实时状态

                now.setInspectionTaskNo(taskInfo.getInspectionTaskNo());
                now.setInspectionTaskName(taskInfo.getInspectionTaskName());
                now.setInspectionType(taskInfo.getInspectionType());
                if ("3".equals(taskInfo.getInspectionTaskStatus())) {
                    return R.data(ResultCode.FAILURE.getCode(), "", "工单已完成，无法回传数据");
                }
            }
            //保存无人机信息
            AppFlyStateParamDTO flyState = appAircraft.getFlight();
            if (flyState == null) {
                return R.data(ResultCode.FAILURE.getCode(), "", "当前无人机信息为空");
            }
            // 推送前端显示实时状态
            now.setAl(flyState.getAl());
            now.setDistance(flyState.getDistance());
            now.setLa(flyState.getLa());
            now.setFlySeconds(flyState.getFlySeconds());
            now.setGpsCount(flyState.getGpsCount());
            now.setFlyState(flyState.getFlyState());
            now.setHSpeed(flyState.getHSpeed());
            now.setVSpeed(flyState.getVSpeed());
            now.setSerialNumber(flyState.getSerialNumber());
            now.setLo(flyState.getLo());
            redis.set(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "flyState", now.getInspectionTaskNo());
            redis.set(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "flyType", now.getInspectionType());
            redis.expire(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "flyState", CommonConstant.VALID_CACHE_TIME_ONE);
            redis.expire(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "flyType", CommonConstant.VALID_CACHE_TIME_ONE);

            FlyRealInfo flyRealinfo = this.saveFlyStateInfo(flyState);
            //保存在飞信息
            saveOnlineSn(flyState.getSerialNumber());
            //保存无人机当次飞行历史轨迹
            saveUavPosition(flyState.getSerialNumber(), flyState.getLo() + " " + flyState.getLa());
            //保存无人机信息到redis
            Map<Object, Object> appFlyMap = Maps.newHashMap();
            appFlyMap.put("appFlyState", flyState);
            redis.hMset(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appFlyState", appFlyMap);
            redis.expire(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appFlyState", CommonConstant.VALID_CACHE_TIME);
            log.info("当前无人机唯一识别码,{}", flyState.getSerialNumber());

            //缓存工单详情信息和状态
            Map<Object, Object> appWorkOrderMap = Maps.newHashMap();
            //反序列化失败
            taskInfo.setCreateUser(null);
            taskInfo.setCreateDept(null);
            appWorkOrderMap.put("appWorkOrder", taskInfo);
            redis.hMset(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appWorkOrder", appWorkOrderMap);
            redis.expire(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appWorkOrder", CommonConstant.VALID_CACHE_TIME);

            //保存云台信息
            List<AppGimbalStateParamDTO> gimbals;
            List<FlyPtz> flyPtzs = new ArrayList<>();
            gimbals = appAircraft.getGimbal();
            if (isReceiveGimbal) {
                if (CollectionUtil.isEmpty(gimbals)) {
                    log.error("云台信息为空!");
                } else {
                    flyPtzs = this.saveGimbalStateInfo(gimbals);
                    HashMap<Object, Object> appGimbalStateMap = Maps.newHashMap();
                    appGimbalStateMap.put("appGimbalState", gimbals);
                    //缓存云台信息
                    redis.hMset(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appGimbalState", appGimbalStateMap);
                    redis.expire(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appGimbalState", CommonConstant.VALID_CACHE_TIME);
                }
            }
            //缓存电池信息
            List<AppBatteryStateParamDTO> appBatteryStateParamDTOS = appAircraft.getBattery();
            if (CollectionUtil.isEmpty(appBatteryStateParamDTOS)) {
                log.error("无人机电池信息为空!");
                //return R.data(ResultCode.FAILURE.getCode(),"","飞机电池信息为空");
            } else {
                // 推送前端显示实时状态
                now.setChargeRemainingInPercent(appBatteryStateParamDTOS.get(0).getChargeRemainingInPercent());
                Map<Object, Object> appBatteryStateMap = Maps.newHashMap();
                appBatteryStateMap.put("appBatteryState", appBatteryStateParamDTOS);
                redis.hMset(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appBatteryState", appBatteryStateMap);
                redis.expire(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appBatteryState", CommonConstant.VALID_CACHE_TIME);
            }
            //缓存遥控器信息
            AppRemoteStateParamDTO appRemoteStateParamDTO = appAircraft.getRemote();
            if (appRemoteStateParamDTO == null) {
                log.error("无人机遥控器信息为空!");
                //return R.data(ResultCode.FAILURE.getCode(),"","遥控器信息为空");
            } else {
                Map<Object, Object> appRemoteStateMap = Maps.newHashMap();
                appRemoteStateMap.put("appRemoteState", appRemoteStateParamDTO);
                redis.hMset(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appRemoteState", appRemoteStateMap);
                redis.expire(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appRemoteState", CommonConstant.VALID_CACHE_TIME);
            }

            //保存飞行任务信息
            //获取app登录相关信息
            AppWorkerStateParamDTO workerState = appAircraft.getWorker();
            this.saveTaskStateInfo(taskState, flyState, flyRealinfo, flyPtzs, workerState, taskInfo);
            //缓存架次信息
            Map<Object, Object> appTaskStateMap = Maps.newHashMap();
            appTaskStateMap.put("appTaskState", taskState);
            redis.hMset(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appTaskState", appTaskStateMap);
            redis.expire(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appTaskState", CommonConstant.VALID_CACHE_TIME);

            //转换成国华首页和三维需展示的对象并缓存
            AppReceiveDataParamDTO appReceiveDataParamDTO = convertFrontView(flyState, appBatteryStateParamDTOS, taskState, appRemoteStateParamDTO, gimbals);
            Map<Object, Object> appReceiveDataParamMap = new HashMap<>();
            appReceiveDataParamMap.put("appFlyDataVo", appReceiveDataParamDTO);
            redis.hMset(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appFlyDataVo", appReceiveDataParamMap);
            redis.expire(CommonConstant.APP_REDIS + flyState.getSerialNumber() + "appFlyDataVo", CommonConstant.VALID_CACHE_TIME);
            stopWatch.stop();
            log.info("接口:{},接收移动端在飞无人机数据集合结束==SerialNumber:{},用时:{}/毫秒", stopWatch.getId(), flyState.getSerialNumber(), stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return R.data(ResultCode.FAILURE.getCode(), "", "接收失败");
        }
        MessageStructForApp struct = new MessageStructForApp();
        struct.setMessageBody(now);
        String uavSn = now.getSerialNumber();
        log.info("====================发送 websocket===========/topic/public/" + uavSn + "==============================");
        log.info("====================发送 websocket===========struct" + JSON.toJSONString(struct) + "==============================");

        mqTool.sendMsgToAllByTopicEnd(uavSn, struct);
        return R.data(ResultCode.SUCCESS.getCode(), "", "回传成功");
    }

    private void saveOnlineSn(String serialNumber) {
        ArrayList<String> list = redis.get(CommonConstant.APP_REDIS + "onlineSnList");
        if (CollectionUtil.isNotEmpty(list)) {
            if (!list.contains(serialNumber)) {
                list.add(serialNumber);
                redis.setEx(CommonConstant.APP_REDIS + "onlineSnList", list, CommonConstant.VALID_CACHE_TIMES);
            }
        } else {
            redis.setEx(CommonConstant.APP_REDIS + "onlineSnList", new ArrayList<>(Collections.singleton(serialNumber)), CommonConstant.VALID_CACHE_TIMES);
        }
    }
    private AppReceiveDataParamDTO convertFrontView(AppFlyStateParamDTO flyState, List<AppBatteryStateParamDTO> appBatteryStateParamDTOS, AppTaskStateParamDTO taskState, AppRemoteStateParamDTO remote, List<AppGimbalStateParamDTO> gimbals) {
        AppReceiveDataParamDTO app = new AppReceiveDataParamDTO();
        try {
            //无人机唯一识别码
            app.setEquipmentNo(flyState.getSerialNumber());
            app.setAppCode(flyState.getSerialNumber());
            //飞行仪表盘
            app.setSpeed(flyState.getHSpeed() == null ? "" : String.valueOf(flyState.getHSpeed()));
            app.setLatitude(flyState.getLa() == null ? "" : String.valueOf(flyState.getLa()));
            app.setDistance(flyState.getDistance() == null ? "" : String.valueOf(flyState.getDistance()));
            app.setTime(flyState.getFlySeconds() == null ? "" : String.valueOf(flyState.getFlySeconds()));
            app.setAltitude(flyState.getAl() == null ? "" : String.valueOf(flyState.getAl()));
            app.setLongitude(flyState.getLo() == null ? "" : String.valueOf(flyState.getLo()));
            //基本信息
            if (remote != null) {
                app.setControlSignal(remote.getSignalLevel() == null ? "" : String.valueOf(remote.getSignalLevel()));
                //图传信号就是遥控信号
                app.setPictureSignal(remote.getSignalLevel() == null ? "" : String.valueOf(remote.getSignalLevel()));
                app.setControlPower(remote.getRemainingChargeInPercent() == null ? "" : String.valueOf(remote.getRemainingChargeInPercent()));
            }
            //飞机电池电量现在展示剩余电量百分比
            if (CollectionUtil.isNotEmpty(appBatteryStateParamDTOS)) {
                app.setPlanePower(appBatteryStateParamDTOS.get(0).getChargeRemainingInPercent() == null ? "" : String.valueOf(appBatteryStateParamDTOS.get(0).getChargeRemainingInPercent()));
                app.setBatteryTemperature(appBatteryStateParamDTOS.get(0).getTemperature() == null ? "" : String.valueOf(appBatteryStateParamDTOS.get(0).getTemperature()));
                app.setCurrent(appBatteryStateParamDTOS.get(0).getCurrent() == null ? "" : String.valueOf(appBatteryStateParamDTOS.get(0).getCurrent()));
                app.setVoltage(appBatteryStateParamDTOS.get(0).getVoltage() == null ? "" : String.valueOf(appBatteryStateParamDTOS.get(0).getVoltage()));
            }
            //其他信息
            app.setFlyStatus(flyState.getFlyState() == null ? "" : String.valueOf(flyState.getFlyState()));
            app.setInspecGuid(taskState.getInspectGuid());
            app.setRecGuid(taskState.getFlyGuid());
            app.setStartTime(taskState.getLaunchTime() == null ? null : timeStamToDatetime(taskState.getLaunchTime()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            app.setEndTime(taskState.getLandTime() == null ? null : timeStamToDatetime(taskState.getLandTime()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            //数据单位转换
            //秒转分钟
            String totalTime = new BigDecimal(StringUtil.isEmpty(app.getTime()) ? "0" : app.getTime()).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP).toString();
            app.setTime(totalTime);
            //米转公里
            String planeFlyMileage = new BigDecimal(StringUtil.isEmpty(app.getDistance()) ? "0" : app.getDistance()).divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_UP).toString();
            app.setDistance(planeFlyMileage);
            //电压 mv转V 保留两位小数
            String vo = new BigDecimal(StringUtil.isEmpty(app.getVoltage()) ? "0" : app.getVoltage()).divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_UP).toString();
            app.setVoltage(vo);
            //电压 ma转A 保留两位小数
            String cu = new BigDecimal(StringUtil.isEmpty(app.getCurrent()) ? "0" : app.getCurrent()).divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_UP).toString();
            app.setCurrent(cu);
            //速度保留两位小数
            String speed = new BigDecimal(StringUtil.isEmpty(app.getSpeed()) ? "0" : app.getSpeed()).divide(new BigDecimal(1), 2, BigDecimal.ROUND_HALF_UP).toString();
            app.setSpeed(speed);
            //相对高度保留两位小数
            String height = new BigDecimal(StringUtil.isEmpty(app.getAltitude()) ? "0" : app.getAltitude()).divide(new BigDecimal(1), 2, BigDecimal.ROUND_HALF_UP).toString();
            app.setAltitude(height);
            app.setUserGuid(AuthUtil.getUserId());
            if (CollectionUtil.isNotEmpty(gimbals)) {
                app.setRollAngle(String.valueOf(gimbals.get(0).getRoll()));
                app.setPitchAngle(String.valueOf(gimbals.get(0).getPitch()));
                app.setYawAngle(String.valueOf(gimbals.get(0).getYaw()));
            }
        } catch (Exception e) {
            log.error("数据转换异常:{}", e.toString());
        }
        return app;
    }

    private void saveTaskStateInfo(AppTaskStateParamDTO taskState, AppFlyStateParamDTO flyState, FlyRealInfo flyRealinfo, List<FlyPtz> flyPtzs, AppWorkerStateParamDTO workerState, InspectionTask taskInfo) {
        FlyFlightSorties flyFlightSorties = new FlyFlightSorties();
        flyFlightSorties.setFlySeconds(flyState.getFlySeconds());
        flyFlightSorties.setFlyDistance(flyState.getDistance());
        flyFlightSorties.setSerialNumber(flyState.getSerialNumber());
        flyFlightSorties.setFlightGuid(taskState.getFlyGuid());
        flyFlightSorties.setDeviceType(taskState.getDeviceType());
        flyFlightSorties.setDeviceId(taskState.getDeviceGuid());
        flyFlightSorties.setHighPositionY(taskState.getHPositionY());
        flyFlightSorties.setHighPositionX(taskState.getHPositionX());
        flyFlightSorties.setHighTemperature(taskState.getHighTemperature());
        flyFlightSorties.setLowTemperature(taskState.getLowTemperature());
        flyFlightSorties.setLowPositionX(taskState.getLPositionX());
        flyFlightSorties.setLowPositionY(taskState.getLPositionY());
        flyFlightSorties.setGroupGuid(taskState.getGroupGuid());
        flyFlightSorties.setMissionType(taskState.getMissionType());
        flyFlightSorties.setMissionTotal(taskState.getTotalMissionCount());
        flyFlightSorties.setMissionExecutedCount(taskState.getMissionCount());
        flyFlightSorties.setUserId(taskState.getInspectGuid());
        flyFlightSorties.setCreateTime(new Date());
        flyFlightSorties.setDeptCode(AuthUtil.getDeptCode());
        //回传设备类型不符合规则时转换为工单设备类型
        if (null == flyFlightSorties.getDeviceType() || 0 > flyFlightSorties.getDeviceType() || 2 < flyFlightSorties.getDeviceType()) {
            switch (taskInfo.getInspectionType()) {
                case "TMS_LINE":
                    flyFlightSorties.setDeviceType(0);
                    break;
                case "FAN":
                    flyFlightSorties.setDeviceType(1);
                    break;
                case "PV":
                    flyFlightSorties.setDeviceType(2);
                    break;
            }
        }
        if (workerState != null) {
            //飞手guid
            flyFlightSorties.setUserId(workerState.getWorkerGuid());
            flyFlightSorties.setDjUser(workerState.getDjUser());
        }

        flyFlightSorties.setLandTime(taskState.getLandTime() == null || taskState.getLandTime() == 0 ? null : timeStamToDatetime(taskState.getLandTime()));
        flyFlightSorties.setLaunchTime(taskState.getLaunchTime() == null || taskState.getLaunchTime() == 0 ? null : timeStamToDatetime(taskState.getLaunchTime()));

        List<FlyFlightSorties> flightSorties = flyFlightSortiesService.list(new LambdaQueryWrapper<FlyFlightSorties>().eq(FlyFlightSorties::getFlightGuid, flyFlightSorties.getFlightGuid()));

        if (CollUtil.isEmpty(flightSorties)) {
            flyFlightSortiesService.save(flyFlightSorties);
        } else {
            flyFlightSorties.setId(flightSorties.get(0).getId());
            flyFlightSortiesService.updateById(flyFlightSorties);
        }
        //更新无人机信息表
        flyRealinfo.setFlightGuid(flyFlightSorties.getFlightGuid());
        flyRealInfoService.updateById(flyRealinfo);
        //更新云台信息表
        flyPtzs.forEach(x -> {
            x.setFlySerialNumber(flyRealinfo.getSerialNumber());
            x.setFlightGuid(flyFlightSorties.getFlightGuid());
        });
        flyPtzService.updateBatchById(flyPtzs);
    }

    private List<FlyPtz> saveGimbalStateInfo(List<AppGimbalStateParamDTO> gimbals) {
        //云台信息入库 转换
        List<FlyPtz> flyPtzs = gimbals.stream().map(x -> {
            FlyPtz data = new FlyPtz();
            data.setPtzGuid(CommonUtil.generateUuid());
            data.setPtzSerialNumber(x.getSerialNumber());
            data.setPtzDisplayName(x.getDisplayName());
            data.setPitch(x.getPitch());
            data.setRoll(x.getRoll());
            data.setYaw(x.getYaw());
            data.setCreateTime(LocalDateTime.now());
            return data;
        }).collect(Collectors.toList());
        flyPtzService.saveBatch(flyPtzs);
        return flyPtzs;
    }

    private FlyRealInfo saveFlyStateInfo(AppFlyStateParamDTO flight) {
        //在飞信息入库 转换
        FlyRealInfo flyRealinfo = convertFlyState(flight);
        flyRealInfoService.save(flyRealinfo);
        return flyRealinfo;
    }

    private FlyRealInfo convertFlyState(AppFlyStateParamDTO flight) {
        FlyRealInfo flyRealinfo = new FlyRealInfo();
        flyRealinfo.setRealGuid(CommonUtil.generateUuid());
        flyRealinfo.setFlyState(flight.getFlyState());
        flyRealinfo.setFlyMessage(flight.getFlyStr());
        flyRealinfo.setGpsCount(flight.getGpsCount());
        flyRealinfo.setRealAlt(flight.getAl());
        flyRealinfo.setRealLat(flight.getLa());
        flyRealinfo.setRealLon(flight.getLo());
        flyRealinfo.setRealHorizontalSpeed(flight.getHSpeed());
        flyRealinfo.setRealVerticalSpeed(flight.getVSpeed());
        flyRealinfo.setRtkState(flight.getRtkState());
        flyRealinfo.setSerialNumber(flight.getSerialNumber());
        flyRealinfo.setModelName(flight.getModelName());
        flyRealinfo.setYaw(flyRealinfo.getYaw());
        flyRealinfo.setCreateTime(LocalDateTime.now());
        return flyRealinfo;
    }

    /**
     * 此接口没有返回升压站类型工单
     *
     * @param param
     * @return
     */
    @Override
    public R queryWorkOrderList(String param) {
        log.info("查询巡检工单信息接口获取加密后数据串：{}", param);
        StopWatch stopWatch = new StopWatch("queryWorkOrderList");
        stopWatch.start();
        String userId = AuthUtil.getUserId();
        String responseString = "";
        try {
            param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            JSONObject paramMap = JSONObject.parseObject(param);
            //app端页码从0开始算，所以+1
            Integer pageNo = paramMap.get("pageNo") == null ? 1 : paramMap.getInteger("pageNo") + 1;
            Integer pageSize = paramMap.get("pageSize") == null ? 10 : paramMap.getInteger("pageSize");
            Map<String, Object> map = new HashMap<>();
            map.put("userId", userId);
            map.put("pageNo", pageNo);
            map.put("pageSize", pageSize);
            R<AppPageVO<AppWorkOrderVO>> flyTaskInfoByUser = iFlyTaskClient.getOrderByCommander(map);
            if (flyTaskInfoByUser.getCode() == ResultCode.SUCCESS.getCode() && flyTaskInfoByUser.getData() != null) {
                AppPageVO<AppWorkOrderVO> result = flyTaskInfoByUser.getData();
                result.getData().forEach(e -> {
                    e.setRouteGuid("123123");
                });
                log.info("加密前工单返回数据:{}", JSON.toJSONString(result.getData()));
                responseString = AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, JSON.toJSONString(result.getData()));
            } else if (flyTaskInfoByUser.getCode() != ResultCode.SUCCESS.getCode()) {
                return R.data(ResultCode.FAILURE.getCode(), "", "获取工单信息失败");
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.error("获取工单信息失败", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "获取工单信息失败");
        }
        stopWatch.stop();
        log.info("接口:{},获取当前用户负责的巡检工单详细信息,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
        return R.data(responseString);
    }

    @Override
    public R queryAppInspectionTaskPage(String pageNo, String pageSize, String deviceType,
                                        String inspectionTaskName, String inspectionTaskStatus) {
        log.info("查询巡检工单信息接口获取加密后数据串pageSize：{}-deviceType：{}", pageSize,deviceType);
        StopWatch stopWatch = new StopWatch("queryAppInspectionTaskPage");
        stopWatch.start();
        String deptCode = AuthUtil.getDeptCode();
        String userId = AuthUtil.getUserId();
        String responseString = "";
        try {

            //app端页码从0开始算，所以+1
            Integer pageNoNum = StringUtil.isBlank(pageNo)  ? 1 : Integer.parseInt(pageNo) + 1;
            Integer pageSizeNum = StringUtil.isBlank(pageSize) ? 10 : Integer.parseInt(pageSize);
             inspectionTaskName = StringUtil.isBlank(inspectionTaskName)? "" : inspectionTaskName;
             inspectionTaskStatus = StringUtil.isBlank(inspectionTaskStatus)? "" : inspectionTaskStatus;
             deviceType = StringUtil.isBlank(deviceType) ? "PV" : deviceType;
            Map<String, Object> map = new HashMap<>();
            map.put("userId", userId);
            map.put("pageNo", pageNoNum);
            map.put("pageSize", pageSizeNum);
            map.put("deviceType", deviceType);
            map.put("deptCode", deptCode);
            if (StringUtil.isNotBlank(inspectionTaskName)) {
                map.put("inspectionTaskName", inspectionTaskName);
            }
            if (StringUtil.isNotBlank(inspectionTaskStatus)) {
                map.put("inspectionTaskStatus", inspectionTaskStatus);
            }
            R<AppPageVO<AppInspectionTaskVO>> taskInfo = iFlyTaskClient.getAppInspectionTaskVO(map);
            if (taskInfo.getCode() == ResultCode.SUCCESS.getCode() && taskInfo.getData() != null) {
                AppPageVO<AppInspectionTaskVO> result = taskInfo.getData();
                log.info("加密前工单返回数据:{}", JSON.toJSONString(result.getData()));
                return R.data(result.getData());
            } else {
                return R.data(ResultCode.FAILURE.getCode(), "", "获取工单信息失败");
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.error("获取工单信息失败", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "获取工单信息失败");
        }finally {
            stopWatch.stop();
            log.info("接口:{},查询巡检工单信息,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
        }

    }

    @Override
    public R queryAppTaskReport(String deviceId, String inspectionTaskId, String deviceType) {
        log.info("查询缺陷统计信息任务id：{}-设备id：{}", inspectionTaskId,deviceId);
        StopWatch stopWatch = new StopWatch("queryAppTaskReport");
        stopWatch.start();
        try {

             inspectionTaskId = StringUtil.isBlank(inspectionTaskId)? "" : inspectionTaskId;
             deviceId =StringUtil.isBlank(deviceId) ? "" : deviceId;
             deviceType = StringUtil.isBlank(deviceType)? "" : deviceType;
            Map<String, Object> map = new HashMap<>();
            map.put("deviceType", deviceType);
            if (StringUtil.isNotBlank(inspectionTaskId)) {
                map.put("inspectionTaskId", inspectionTaskId);
            }
            if (StringUtil.isNotBlank(deviceId)) {
                map.put("deviceId", deviceId);
            }
            R<AppInspectionTaskOnlineReportingVO> taskInfo = iFlyTaskClient.queryAppTaskReport(map);
            if (taskInfo.getCode() == ResultCode.SUCCESS.getCode() && taskInfo.getData() != null) {
                AppInspectionTaskOnlineReportingVO result = taskInfo.getData();
                log.info("加密前工单返回数据:{}", JSON.toJSONString(result));
                return R.data(result);
            } else {
                return R.data(ResultCode.FAILURE.getCode(), "", "获取工单信息失败");
            }

        } catch (Exception e) {
            stopWatch.stop();
            log.error("查询缺陷统计信息失败", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "查询缺陷统计信息失败");
        } finally {
            stopWatch.stop();
            log.info("接口:{},查询缺陷统计信息,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
        }

    }

    @Override
    public R appGetRoute(String deviceId,  String inspectionTaskId) {
        log.info("查询航线任务id：{}-设备id：{}", inspectionTaskId,deviceId);
        StopWatch stopWatch = new StopWatch("appGetRoute");
        stopWatch.start();
        try {
            if (StringUtil.isBlank(deviceId)) {
                return R.data(ResultCode.FAILURE.getCode(), "", "获取航线信息失败");
            }
            Map<String, Object> map = new HashMap<>();
            if (StringUtil.isNotBlank(inspectionTaskId)) {
                map.put("inspectionTaskId", inspectionTaskId);
            }
            map.put("deviceId", deviceId);

            R<List<AppPvTrackVO>> routeInfo = iFlyTaskClient.appGetRoute(map);
            if (routeInfo.getCode() == ResultCode.SUCCESS.getCode() && routeInfo.getData() != null) {
                List<AppPvTrackVO> result = routeInfo.getData();
                log.info("加密前航线返回数据:{}", JSON.toJSONString(result));
                return R.data(result);
            } else{
                return R.data(ResultCode.FAILURE.getCode(), "", "获取航线信息失败");
            }


        } catch (Exception e) {
            stopWatch.stop();
            log.error("获取航线信息失败", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "获取航线信息失败");
        } finally {
            stopWatch.stop();
            log.info("接口:{},查询缺陷统计信息,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
        }
    }

    @Override
    public R appGetPicByDevice(String deviceId, String inspectionTaskId) {
        log.info("查询任务下的设备图任务id：{}-设备id：{}", inspectionTaskId,deviceId);
        StopWatch stopWatch = new StopWatch("appGetPicByDevice");
        stopWatch.start();
        try {

            if (StringUtil.isBlank(deviceId) || StringUtil.isBlank(inspectionTaskId)) {
                return R.data(ResultCode.FAILURE.getCode(), "", "参数缺失，app获取设备图失败");
            }
            Map<String, Object> map = new HashMap<>();
            map.put("inspectionTaskId", inspectionTaskId);
            map.put("deviceId", deviceId);

            R<List<List<String>>> picInfo = iFlyTaskClient.appGetPicByDevice(map);
            if (picInfo.getCode() == ResultCode.SUCCESS.getCode() && picInfo.getData() != null) {
                List<List<String>> result = picInfo.getData();
                log.info("加密前设备图返回数据:{}", JSON.toJSONString(result));
                return R.data(result);
            } else {
                return R.data(ResultCode.FAILURE.getCode(), "", "app获取设备图失败");
            }

        } catch (Exception e) {
            stopWatch.stop();
            log.error("app获取设备图失败", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "app获取设备图失败");
        } finally {
            stopWatch.stop();
            log.info("接口:{},查询任务下的设备图,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
        }

    }

    @Override
    public R getPvStatistics(String deviceId, String inspectionTaskId) {
        log.info("查询任务下的设备图任务id：{}-设备id：{}", inspectionTaskId,deviceId);
        StopWatch stopWatch = new StopWatch("getPvStatistics");
        stopWatch.start();
        try {

            if (StringUtil.isBlank(deviceId) || StringUtil.isBlank(inspectionTaskId)) {
                return R.data(ResultCode.FAILURE.getCode(), "", "参数缺失，app获取光伏统计信息失败");
            }
            Map<String, Object> map = new HashMap<>();
            map.put("inspectionTaskId", inspectionTaskId);
            map.put("deviceId", deviceId);
            map.put("deptId", AuthUtil.getDeptId());
            map.put("deptCode", AuthUtil.getDeptCode());

            R<AppStatisticVO> info = iFlyTaskClient.getPvStatistics(map);
            if (info.getCode() == ResultCode.SUCCESS.getCode() && info.getData() != null) {
                AppStatisticVO result = info.getData();
                log.info("加密前设备图返回数据:{}", JSON.toJSONString(result));
                return R.data(result);
            } else {
                return R.data(ResultCode.FAILURE.getCode(), "", "app获取光伏统计信息失败");
            }

        } catch (Exception e) {
            stopWatch.stop();
            log.error("app获取光伏统计信息失败", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "app获取光伏统计信息失败");
        } finally {
            stopWatch.stop();
            log.info("接口:{},app获取光伏统计信息,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
        }
    }

    @Override
    public R queryWorkOrderDetails(String param) {
        log.info("查询巡检工单详情接口获取加密后数据串：{}", param);
        StopWatch stopWatch = new StopWatch("queryWorkOrderDetails");
        stopWatch.start();
        AppWorkOderDetailResponseVO appWorkOderDetailResponseVO = new AppWorkOderDetailResponseVO();
        String responseString = "";
        try {
            param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            JSONObject paramMap = JSONObject.parseObject(param);
            if (null == paramMap.get("inspectGuid")) {
                return R.data(ResultCode.FAILURE.getCode(), "", "工单guid为空");
            }
            String inspectGuid = paramMap.getString("inspectGuid");
            R<List<AppWorkOderDetailVO>> data = iFlyTaskClient.getOrderDeviceByOrderGuid(inspectGuid);
            if (data.getCode() == ResultCode.SUCCESS.getCode() && data.getData() != null) {
                appWorkOderDetailResponseVO.setData(data.getData());
                responseString = AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, JSON.toJSONString(appWorkOderDetailResponseVO));
            } else if (data.getCode() != ResultCode.SUCCESS.getCode()) {
                return R.data(ResultCode.FAILURE.getCode(), "", "获取工单详情失败");
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.error("获取工单详情失败", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "获取工单详情失败");
        }
        stopWatch.stop();
        log.info("接口:{},获取当前用户负责的巡检工单详细信息,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
        return R.data(responseString);
    }

    @Override
    public R appReceiveFanPic(String param, MultipartFile file) {
        log.info("app保存风机巡检图片信息接口获取加密后数据串：{}", param);
        R<AllcoreFileVO> upload = new R<AllcoreFileVO>();
        try {
            param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            JSONObject paramMap = JSONObject.parseObject(param);
            //解决文件名乱码
            String picFileName = paramMap.getString("fileName");
            if (StringUtil.isNotBlank(picFileName)) {
                file = new MockMultipartFile(picFileName, picFileName, file.getContentType(), file.getInputStream());
            }
            //图片上传
            upload = ossClient.putFileAttach("original_picture", file, "yes", null);

        } catch (Exception e) {
            log.error("保存风机巡检图片信息失败,错误信息:{}", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "保存风机巡检图片信息失败");
        }
        Gson gson = new Gson();
        String jsonString = gson.toJson(upload.getData());
        log.info("app保存风机巡检图片成功：{}", jsonString);
        return R.data(jsonString);
    }

    @Override
    public R folderUpload(String param) {
        log.info("app保存风机巡检图片信息接口获取加密后数据串：{}", param);
        try {

            param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            FolderUpLoadDTO appAircraft = JSONObject.parseObject(param, FolderUpLoadDTO.class);
            log.info("风机文件夹上传接口获取解密后数据串：{}", JSON.toJSONString(appAircraft));
            if (null == appAircraft.getInspectionTaskId()) {
                return R.data(ResultCode.FAILURE.getCode(), "", "工单guid为空");
            }
            String inspectGuid = appAircraft.getInspectionTaskId();
            R<InspectionTask> data = iFlyTaskClient.getOrderByGuid(inspectGuid);
            if (data.getCode() == ResultCode.SUCCESS.getCode() && data.getData() != null) {
                appAircraft.setInspectionTaskNo(data.getData().getInspectionTaskNo());
                appAircraft.setDeptCode(data.getData().getDeptCode());
                R r = iFlyTaskClient.fanFolderUpload(appAircraft);
                if (data.getCode() == ResultCode.SUCCESS.getCode() && data.getData() != null) {
                    return R.data(ResultCode.SUCCESS.getCode(), r.getData(), "保存风机巡检图片信息成功");
                }

            } else if (data.getCode() != ResultCode.SUCCESS.getCode()) {
                return R.data(ResultCode.FAILURE.getCode(), "", "获取工单详情失败");
            }


        } catch (Exception e) {
            log.error("保存巡检图片信息失败,错误信息:{}", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "保存巡检图片信息失败");
        }
        return R.data(ResultCode.FAILURE.getCode(), "", "保存风机巡检图片信息失败");
    }

    @Override
    public R appReceivePic(String param, MultipartFile file) {
        log.info("app保存巡检图片信息接口获取加密后数据串：{}", param);
        StopWatch stopWatch = new StopWatch("appReceivePic");
        stopWatch.start();
        try {

            param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            JSONObject paramMap = JSONObject.parseObject(param);
            //解决文件名乱码
            String picFileName = paramMap.getString("fileName");
            if (StringUtil.isNotBlank(picFileName)) {
                file = new MockMultipartFile(picFileName, picFileName, file.getContentType(), file.getInputStream());
            }
            String result = picParamVerify(paramMap, "");
            if (!StringUtil.isEmpty(result)) {
                log.error("参数校验失败：{}", result);
                return R.data(ResultCode.FAILURE.getCode(), StringPool.EMPTY, result);
            }
            //工单guid
            String inspecGuid = paramMap.getString("inspectGuid");
            //查询工单信息
            R<InspectionTask> flyTaskInfoByGuid = iFlyTaskClient.getOrderByGuid(inspecGuid);
            if (!flyTaskInfoByGuid.isSuccess() || null == flyTaskInfoByGuid.getData()) {
                return R.data(ResultCode.FAILURE.getCode(), "", "保存巡检图片信息失败");
            }
            if (flyTaskInfoByGuid.getData().getInspectionTaskStatus().equals("3")) {
                return R.data(ResultCode.FAILURE.getCode(), "", "保存巡检图片失败！工单已完结！");
            }
            //巡检设备类型
            int type = paramMap.getInteger("deviceType");
            //设备guid(杆塔、风机、子阵等)
            String deviceGuid = paramMap.getString("deviceGuid");
            //存入Redis，记录工单状态(图片上传中)
            redis.setEx(CommonConstant.APP_REDIS + AppWorkOrderStatusDTO.imageUploadKey + inspecGuid, AppWorkOrderStatusDTO.Status.ImageUpload.getCode(), 60L);
            try {
                String md5 = paramMap.getString("key");
                //生成缺陷图片数据
                R r = this.createAppInspectionPictureDetail(flyTaskInfoByGuid.getData(), file, deviceGuid, type, md5);
                if (!r.isSuccess()) {
                    log.error("保存pictureDetail失败，当前设备guid:{}", deviceGuid);
                    return R.data(ResultCode.FAILURE.getCode(), "", "保存巡检图片信息失败(保存pictureDetail失败)");
                }

                // 保存算法盒子识别的缺陷信息
                /*String defectTaggingCollectStr = paramMap.getString("defectTaggingCollect");
                if (StringUtils.isNotBlank(defectTaggingCollectStr)) {
                    log.info("保存巡检图片信息：{}", defectTaggingCollectStr);
                    R<Boolean> booleanR = iFlyTaskClient.saveDefectTaggingInfoCollect(JSON.parseObject(defectTaggingCollectStr, DefectTaggingInfoCollectDTO.class));
                    if (!booleanR.isSuccess()) {
                        log.error("保存DefectTaggingInfoCollect失败，当前设备guid:{}", deviceGuid);
                        return R.data(ResultCode.FAILURE.getCode(), "", "保存巡检图片信息失败(保存DefectTaggingInfoCollect失败)");
                    }
                }*/
            } catch (Exception e) {
                log.error("文件上传minio失败,当前设备guid:{},错误信息:{}", deviceGuid, e.toString());
                return R.data(ResultCode.FAILURE.getCode(), "", "保存巡检图片信息失败(图片上传失败)");
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.error("保存巡检图片信息失败,错误信息:{}", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "保存巡检图片信息失败");
        }
        stopWatch.stop();
        log.info("接口:{},保存巡检图片信息,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
        return R.data(ResultCode.SUCCESS.getCode(), "", "保存巡检图片信息成功");
    }


    @Override
    public R updateInspectStatus(String param) {
        log.info("app更新设备执行状态数据串：{}", param);
        try {
            param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            JSONObject paramMap = JSONObject.parseObject(param);
            log.info("app更新设备执行状态解密数据串：{}", paramMap);
            if (ObjectUtil.isEmpty(paramMap.get("inspectGuid")) || ObjectUtil.isEmpty(paramMap.get("deviceGuid"))) {
                return R.data(ResultCode.FAILURE.getCode(), "", "工单guid或设备信息为空！");
            }
            iFlyTaskClient.updateAppInspectStatus(String.valueOf(paramMap.get("inspectGuid")), String.valueOf(paramMap.get("deviceGuid")));
        } catch (Exception e) {
            log.error("app更新设备执行状态失败！");
        }
        return R.success("更新成功！");
    }

    @Override
    public R flyAirSpace(String param) {
        //TODO 获取空域信息(禁飞区、适航区、限飞区等)，国华新能源可能不涉及
        return R.success("");
    }

    @Override
    public R deviceProtocol(String param, HttpServletRequest httpServletRequest) {
        log.info("下载航线文件接口获取加密后数据串：{}", param);
        StopWatch stopWatch = new StopWatch("deviceProtocol");
        stopWatch.start();
        String responseString = "";
        try {
            param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            JSONObject paramMap = JSONObject.parseObject(param);
            String deviceGuid = paramMap.getString("deviceGuid");
            String deviceType = paramMap.getString("deviceType");
            if (StringUtil.isEmpty(deviceGuid)) {
                return R.data(ResultCode.FAILURE.getCode(), "", "设备guid必填");
            }
            Map<String, String> map = new HashMap<>(2);
            map.put("deviceGuid", deviceGuid);
            map.put("deviceType", deviceType);
            R<AppRouteResponseVO> result = iFlyTaskClient.getRouteInfo(map);
            stopWatch.stop();
            log.info("接口:{},下载航线文件,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
            if (result.getCode() == ResultCode.SUCCESS.getCode() && result.getData() != null) {
                log.info("返回航迹信息:{}", result.getData());
                responseString = com.allcore.core.tool.utils.AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, JSON.toJSONString(result.getData()));
                return R.data(responseString);
            } else if (result.getCode() != ResultCode.SUCCESS.getCode()) {
                return R.data(ResultCode.FAILURE.getCode(), "", "下载航线文件失败");
            } else {
                return R.data(ResultCode.FAILURE.getCode(), "", "暂无承载数据");
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.error("下载航线文件失败,错误信息:{}", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "下载航线文件失败");
        }
    }


    @Override
    public R groupInfoList(String param) {
        log.info("获取设备台账信息接口获取加密后数据串：{}", param);
        StopWatch stopWatch = new StopWatch("groupInfoList");
        stopWatch.start();
        String responseString = "";
        String userId = AuthUtil.getUserId();
        log.info("从本地线程中获取的userId:{}", userId);
        String deptId = AuthUtil.getDeptId();
        log.info("从本地线程中获取的deptId:{}", deptId);
        try {
            Map<String, Object> map = new HashMap<>(6);
            map.put("deptId", deptId);
            //分页查询线路、风机、光伏信息
            R<List<AppLedgerDetailVO>> response = iFlyTaskClient.getDeviceByDeptId(map);
            stopWatch.stop();
            log.info("接口:{},查询设备台账信息,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
            if (response.getCode() == ResultCode.SUCCESS.getCode() && response.getData() != null) {
                responseString = AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, JSON.toJSONString(response.getData()));
                return R.data(responseString);
            } else if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                return R.data(ResultCode.FAILURE.getCode(), "", "查询设备台账信息失败");
            } else {
                return R.data(ResultCode.FAILURE.getCode(), "", "暂无承载数据");
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.error("查询设备台账信息失败,错误信息:{}", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "查询设备台账信息失败");
        }
    }

    @Override
    public R deviceInfoList(String param) {
        log.info("查询设备台账详细信息接口获取加密后数据串：{}", param);
        StopWatch stopWatch = new StopWatch("deviceInfoList");
        stopWatch.start();
        String responseString = "";
        try {
            param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
            JSONObject paramMap = JSONObject.parseObject(param);
            if (null == paramMap.get("groupGuid")) {
                return R.data(ResultCode.FAILURE.getCode(), "", "groupGuid参数不能为空");
            }
            String groupGuid = paramMap.getString("groupGuid");
            Integer pageNo = paramMap.get("pageNo") == null ? 1 : paramMap.getInteger("pageNo");
            Integer pageSize = paramMap.get("pageSize") == null ? 10 : paramMap.getInteger("pageSize");
            Map<String, Object> map = new HashMap<>(6);
            map.put("groupGuid", groupGuid);
            map.put("pageNo", pageNo);
            map.put("pageSize", pageSize);
            R response = iFlyTaskClient.getDeviceByGroupGuid(map);
            stopWatch.stop();
            log.info("接口:{},查询设备台账详细信息,用时:{}/毫秒", stopWatch.getId(), stopWatch.getTotalTimeMillis());
            if (response.getCode() == ResultCode.SUCCESS.getCode() && response.getData() != null) {
                responseString = com.allcore.core.tool.utils.AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, JSON.toJSONString(response.getData()));
                return R.data(responseString);
            } else if (response.getCode() != ResultCode.SUCCESS.getCode()) {
                return R.data(ResultCode.FAILURE.getCode(), "", "查询设备台账详细信息失败");
            } else {
                return R.data(ResultCode.FAILURE.getCode(), "", "暂无承载数据");
            }
        } catch (Exception e) {
            stopWatch.stop();
            log.error("查询设备台账详细信息失败,错误信息:{}", e.toString());
            return R.data(ResultCode.FAILURE.getCode(), "", "查询设备台账详细信息失败");
        }
    }


    private R createAppInspectionPictureDetail(InspectionTask workOrder, MultipartFile file, String deviceGuid, int type, String md5) {
        log.info("开始生成pictureDetail数据！");
        HashMap<String, List<Long>> deviceGroupByType = Maps.newHashMap();
        String typeValue = "";
        switch (type) {
            case 0:
                typeValue = "TMS_LINE";
                break;
            case 1:
                typeValue = "FAN";
                break;
            case 2:
                typeValue = "PV";
                break;
            case 3:
                typeValue = "BOOSTER";
                break;
        }
//        deviceGroupByType.put(typeValue, Collections.singletonList(Long.valueOf(deviceGuid)));
//        R<HashMap<String, String>> result = iFlyTaskClient.getDeviceInformationForOrderPic(deviceGroupByType);
//        String filePath = StringPool.EMPTY;
//        String deviceName;
//        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
//            HashMap<String, String> data = result.getData();
//            if (0 == type) {
//                deviceName = data.get(Long.valueOf(deviceGuid));
//                data.remove(Long.valueOf(deviceGuid));
//                for (Map.Entry<String, String> entry : data.entrySet()) {
//                    deviceName = "/" + entry.getValue() + "/" + deviceName + "/";
//                }
//            } else {
//                deviceName = "/" + data.get(Long.valueOf(deviceGuid)) + "/";
//            }
//            filePath = workOrder.getInspectionTaskNo() + deviceName + file.getOriginalFilename();
//        }
        log.info("开始上传原图！");
        //图片上传
        R<AllcoreFileVO> upload = ossClient.putFileAttach("original_picture", file, "yes", null);
//        R<UploadResultDTO> upload = commonFileClient.feignUpload(file, file.getOriginalFilename(), 3, 1, filePath, md5);
        if (!upload.isSuccess() || null == upload.getData()) {
            return R.data(ResultCode.FAILURE.getCode(), "", "保存巡检图片信息失败");
        }

        InspectionPictureSaveDTO picture = new InspectionPictureSaveDTO();
        picture.setFileGuid(upload.getData().getFileGuid());
        picture.setInspectionTaskId(workOrder.getId());
        picture.setDeviceType(typeValue);
        picture.setDeptCode(AuthUtil.getDeptCode());

        List<InspectionPictureSaveDTO> inspectionPicture = new ArrayList<>();
        inspectionPicture.add(picture);

        BindPicDTO bindPicDTO = new BindPicDTO();
        bindPicDTO.setDeviceId(deviceGuid);
        bindPicDTO.setFileGuid(upload.getData().getFileGuid());
        bindPicDTO.setDeviceType(typeValue);
        bindPicDTO.setTaskId(workOrder.getId());

        AppInspectionPictureDTO appInspectionPictureDTO = new AppInspectionPictureDTO();
        appInspectionPictureDTO.setInspectionPicture(inspectionPicture);
        appInspectionPictureDTO.setBindPicDTO(bindPicDTO);
        return iFlyTaskClient.uploadPicForApp(appInspectionPictureDTO);

//        InspectionTaskFileDTO inspectionTaskFileDTO = new InspectionTaskFileDTO();
//        inspectionTaskFileDTO.setFileGuid(upload.getData().getFileGuid());
//        inspectionTaskFileDTO.setFileName(file.getOriginalFilename());
//        inspectionTaskFileDTO.setWebkitRelativePath(filePath);
//
//        ArrayList<InspectionTaskFileDTO> workOrderFileDTOS = new ArrayList<>();
//        workOrderFileDTOS.add(inspectionTaskFileDTO);
//
//        InspectionTaskPicDTO inspectionTaskPicDTO = new InspectionTaskPicDTO();
//        inspectionTaskPicDTO.setWorkOrderGuid(workOrder.getId());
//        inspectionTaskPicDTO.setFiles(workOrderFileDTOS);
//        return iFlyTaskClient.uploadPicForApp(inspectionTaskPicDTO);

    }

//    private void createAppInspectionTask(InspectionTask inspectionTask, String inspecGuid,long createUser, String deptId, long createDeptId,int type,String stationGuid) {
//        inspectionTask.setTaskGuid(CommonUtil.generateUuid());
//        //工单guid
//        inspectionTask.setInspecGuid(inspecGuid);
//        //图片总数
//        //app回传的图片生成的缺陷任务在任务名上加上前缀APP_
//        inspectionTask.setTaskName("APP_自动识别任务" + DateUtil.getStringDateCode());
//        // 识别类型   1自动   2手动
//        inspectionTask.setType(CommonConstant.INT_1);
//        // 上传类型 0 插件    1浏览器 这里都是1
//        inspectionTask.setUploadType(CommonConstant.INT_1);
//        //未执行0，已执行1，状态默认是0未执行
//        inspectionTask.setStatus(CommonConstant.INT_0);
//        //巡检类型  1线路巡检   2风机巡检   3光伏巡检
//        if (CommonConstant.INT_0==type){
//            type = CommonConstant.INT_1;
//        }else if (CommonConstant.INT_2==type){
//            type = CommonConstant.INT_2;
//        }else if (CommonConstant.INT_3==type){
//            type = CommonConstant.INT_3;
//        }
//        inspectionTask.setLineType(String.valueOf(type));
//        inspectionTask.setCreateUser(createUser);
//        //创建人部门id
//        inspectionTask.setCreateDept(createDeptId);
//        //组织机构ID
//        inspectionTask.setDeptId(deptId);
//        inspectionTask.setCreateTime(new Date());
//        inspectionTask.setStationGuid(stationGuid);
//        iInspectionTaskClient.saveInspectionTask(inspectionTask);
//    }

    private String picParamVerify(JSONObject paramMap, String message) {
        if (StringUtil.isEmpty(paramMap.get("inspectGuid"))) {
            return "inspecGuid参数不能为空";
        }
        if (StringUtil.isEmpty(paramMap.get("deviceType"))) {
            return "巡检设备类型不能为空";
        }
        if (StringUtil.isEmpty(paramMap.get("deviceGuid"))) {
            return "巡检设备guid不能为空";
        }
        return message;
    }

    private void saveUavPosition(String sn, String position) {
        ArrayList<String> list = redis.get(CommonConstant.APP_REDIS + sn + "position");
        if (CollectionUtil.isNotEmpty(list)) {
            list.add(position);
        } else {
            list = new ArrayList<String>(Collections.singleton(position));
        }
        redis.setEx(CommonConstant.APP_REDIS + sn + "position", list, CommonConstant.VALID_CACHE_TIMES);
    }

    /**
     * 上传精细学习数据(暂时无处理业务)
     *
     * @param param
     * @return
     */
    @Override
    public R upgradeProtocol(String param) {
        return R.data("");
    }

    /**
     * 获取当前设备精细学习模板 (暂时无处理业务)
     *
     * @return
     */
    @Override
    public R ledgerDeviceModel() {
        ArrayList<String> strings = new ArrayList<>();
        try {
            return R.data(AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, JSON.toJSONString(strings)));
        } catch (Exception e) {
            log.error("加密失败");
        }
        return R.fail("失败");
    }

    /**
     * 获取当前工单杆塔下的精细学习模板暂无处理业务
     *
     * @param param
     * @return
     */
    @Override
    public R orderDeviceModel(String param) {
        ArrayList<String> strings = new ArrayList<>();
        try {
            return R.data(AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, JSON.toJSONString(strings)));
        } catch (Exception e) {
            log.error("加密失败");
        }
        return R.fail("失败");
    }


    /**
     * 时间戳转localdatetime
     *
     * @return
     */
    public LocalDateTime timeStamToDatetime(Long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }
}
