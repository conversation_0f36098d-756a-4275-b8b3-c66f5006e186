package com.allcore.app.code.solve.controller;

import com.allcore.app.code.flyApp.service.IAppMainService;
import com.allcore.app.code.solve.service.IFirstPageService;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.solve.vo.RemoveTaskDefectAppVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 首页
 *
 * <AUTHOR>
 * @date 2023/11/29 13:41
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/removeTask")
@Api(value = "任务列表", tags = "任务列表")
public class RemoveTaskController {

    private final IFirstPageService firstPageService;

    private final IAppMainService appMainService;

    @GetMapping("/pv/list")
    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "任务列表", notes = "")
    public R<List<RemoveTaskDefectAppVO>> removeTaskList(@RequestParam String removeTaskStatus, @RequestParam String timeType) {
        return R.data(firstPageService.removeTaskList(removeTaskStatus, timeType));
    }

    /**
     * 查询巡检工单信息
     * pageNo 第几页
     * pageSize 每页多少条
     * inspectionTaskName 任务名称
     * inspectionTaskStatus 任务状态
     *
     * @param
     * @return
     */
    @PostMapping(value = "/appInspectionTaskPage")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "查询巡检工单信息", notes = "查询巡检工单信息")
    public R appInspectionTaskPage(@RequestParam String pageNo, @RequestParam String pageSize, @RequestParam String deviceType,
                                   @RequestParam String inspectionTaskName, @RequestParam String inspectionTaskStatus) {
        return appMainService.queryAppInspectionTaskPage(pageNo, pageSize, deviceType,
                inspectionTaskName, inspectionTaskStatus);
    }

    /**
     * 查询缺陷统计信息
     * inspectionTaskId 任务id
     * deviceId 任务状态
     *
     * @param
     * @return
     */
    @PostMapping(value = "/appTaskReport")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "查询缺陷统计信息", notes = "查询缺陷统计信息")
    public R appTaskReport(@RequestParam String deviceId, @RequestParam String inspectionTaskId, @RequestParam String deviceType) {
        return appMainService.queryAppTaskReport(deviceId, inspectionTaskId, deviceType);
    }

    /**
     * 查询缺陷统计信息
     * inspectionTaskId 任务id
     * deviceId 任务状态
     *
     * @param
     * @return
     */
    @PostMapping(value = "/appGetRoute")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "查询缺陷统计信息", notes = "查询缺陷统计信息")
    public R appGetRoute(@RequestParam String deviceId, @RequestParam String inspectionTaskId) {
        return appMainService.appGetRoute(deviceId, inspectionTaskId);
    }


    @PostMapping(value = "/getPvStatistics")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "查询缺陷统计信息", notes = "查询缺陷统计信息")
    public R getPvStatistics(@RequestParam String deviceId, @RequestParam String inspectionTaskId) {
        return appMainService.getPvStatistics(deviceId, inspectionTaskId);
    }

    /**
     * 查询任务下的设备图
     * inspectionTaskId 任务id
     * deviceId 任务状态
     *
     * @param
     * @return
     */
    @PostMapping(value = "/appGetPicByDevice")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "查询任务下的设备图", notes = "查询任务下的设备图")
    public R appGetPicByDevice(@RequestParam String deviceId, @RequestParam String inspectionTaskId) {
        return appMainService.appGetPicByDevice(deviceId, inspectionTaskId);
    }
}