package com.allcore.app.code.flyApp.service;


import com.allcore.app.code.flightsorties.dto.AppRemoveDTO;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.inspection.dto.FolderUpLoadDTO;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * @description: APP对接业务服务层
 * @author: wp
 * @date: 2022/3/14
 */
public interface IAppMainService {
    /**
     * 推流地址
     *
     * @param param
     * @return
     */
    R pushMediaUrl(String param);
    /**
     * 接收移动端在飞无人机实时信息
     *
     * @param isOwn
     * @param param
     * @return
     */
    R receiveUavDataNew(boolean isOwn, String param);


    /**
     * 查询工单列表
     *
     * @param param
     * @return
     */
    R queryWorkOrderList(String param);

    /**
     * 查询工单详情
     *
     * @param param
     * @return
     */
    R queryWorkOrderDetails(String param);

    /**
     * 保存巡检图片信息
     *
     * @param param
     * @param file
     * @return
     */
    R appReceivePic(String param, MultipartFile file);


    /**
     * 工单执行状态同步
     *
     * @param param
     * @return
     */
    R updateInspectStatus(String param);

    /**
     * 获取空域信息
     *
     * @param param
     * @return
     */
    R flyAirSpace(String param);

    /**
     * 下载航线文件
     *
     * @param param
     * @return
     */
    R deviceProtocol(String param, HttpServletRequest httpServletRequest);

    /**
     * 查询设备台账信息
     *
     * @param param
     * @return
     */
    R groupInfoList(String param);

    /**
     * 查询设备台账详细信息
     *
     * @param param
     * @return
     */
    R deviceInfoList(String param);

    /**
     * 上传精细学习数据
     * @param param
     * @return
     */
    R upgradeProtocol(String param);

    /**
     * 获取当前设备精细学习模板
     * @return
     */
    R ledgerDeviceModel();

    /**
     * 获取当前工单杆塔下的精细学习模板
     * @param param
     * @return
     */
    R orderDeviceModel(String param);
    /**
     * 保存风机巡检图片信息
     * @param param json字符串
     * @param file 上传的图片
     * @return
     */
    R appReceiveFanPic(String param, MultipartFile file);
    /**
     * 文件夹上传
     */
    R folderUpload(String param);

    /**
     * 查巡检任务
     * @param
     * @return
     */
    R queryAppInspectionTaskPage(String pageNo, String pageSize, String deviceType,
                                 String inspectionTaskName, String inspectionTaskStatus);

    /**
     * 查询缺陷统计信息
     * @param
     * @return
     */
    R queryAppTaskReport(String deviceId, String inspectionTaskId, String deviceType);

    /**
     * 获取解析后的航线
     * @param
     * @return
     */
    R appGetRoute(String deviceId,  String inspectionTaskId);

    /**
     * 查询任务下的设备图
     * @param
     * @return
     */
    R appGetPicByDevice(String deviceId,  String inspectionTaskId);

    /**
     * app获取统计信息
     * @param deviceId
     * @param inspectionTaskId
     * @return
     */
    R getPvStatistics(String deviceId, String inspectionTaskId);
}
