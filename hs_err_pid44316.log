#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 465567744 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=44316, tid=47316
#
# JRE version:  (21.0.6+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'http://***********:8929': 

Host: AMD Ryzen 7 7840H with Radeon 780M Graphics    , 16 cores, 27G,  Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
Time: Tue Jul 29 11:32:14 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.5415) elapsed time: 0.009327 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000255ebc05880):  JavaThread "Unknown thread" [_thread_in_vm, id=47316, stack(0x0000007246600000,0x0000007246700000) (1024K)]

Stack: [0x0000007246600000,0x0000007246700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e5cb9]
V  [jvm.dll+0x8c4113]
V  [jvm.dll+0x8c666e]
V  [jvm.dll+0x8c6d53]
V  [jvm.dll+0x288f76]
V  [jvm.dll+0x6e2575]
V  [jvm.dll+0x6d602a]
V  [jvm.dll+0x3635db]
V  [jvm.dll+0x36b1a6]
V  [jvm.dll+0x3bd4f6]
V  [jvm.dll+0x3bd7c8]
V  [jvm.dll+0x335d2c]
V  [jvm.dll+0x336a1b]
V  [jvm.dll+0x88b569]
V  [jvm.dll+0x3ca6c8]
V  [jvm.dll+0x8745b8]
V  [jvm.dll+0x45f0de]
V  [jvm.dll+0x460dc1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ff99088a148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x00000255ebc992f0 WorkerThread "GC Thread#0"                     [id=45380, stack(0x0000007246700000,0x0000007246800000) (1024K)]
  0x00000255ebca8110 ConcurrentGCThread "G1 Main Marker"            [id=47044, stack(0x0000007246800000,0x0000007246900000) (1024K)]
  0x00000255ebca8b10 WorkerThread "G1 Conc#0"                       [id=50084, stack(0x0000007246900000,0x0000007246a00000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ff98ff78e07]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff9908feb30] Heap_lock - owner thread: 0x00000255ebc05880

Heap address: 0x0000000644c00000, size: 7092 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000644c00000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x0000025580de0000,0x0000025581bc0000] _byte_map_base: 0x000002557dbba000

Marking Bits: (CMBitMap*) 0x00000255ebc998f0
 Bits: [0x0000025581bc0000, 0x0000025588a90000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.005 Loaded shared library D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff7abe60000 - 0x00007ff7abe6a000 	D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\java.exe
0x00007ffa1e550000 - 0x00007ffa1e767000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffa1d830000 - 0x00007ffa1d8f4000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffa1ba20000 - 0x00007ffa1bdf2000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffa1b640000 - 0x00007ffa1b751000 	C:\Windows\System32\ucrtbase.dll
0x00007ffa03c40000 - 0x00007ffa03c5b000 	D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\VCRUNTIME140.dll
0x00007ffa03830000 - 0x00007ffa03848000 	D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\jli.dll
0x00007ffa1c1e0000 - 0x00007ffa1c391000 	C:\Windows\System32\USER32.dll
0x00007ffa1b760000 - 0x00007ffa1b786000 	C:\Windows\System32\win32u.dll
0x00007ffa1d650000 - 0x00007ffa1d679000 	C:\Windows\System32\GDI32.dll
0x00007ffa1bf70000 - 0x00007ffa1c093000 	C:\Windows\System32\gdi32full.dll
0x00007ff9fbad0000 - 0x00007ff9fbd6b000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772\COMCTL32.dll
0x00007ffa1b790000 - 0x00007ffa1b82a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffa1e330000 - 0x00007ffa1e3d7000 	C:\Windows\System32\msvcrt.dll
0x00007ffa1d990000 - 0x00007ffa1d9c1000 	C:\Windows\System32\IMM32.DLL
0x00007ffa13f10000 - 0x00007ffa13f1c000 	D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\vcruntime140_1.dll
0x00007ffa00320000 - 0x00007ffa003ad000 	D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\msvcp140.dll
0x00007ff98fc30000 - 0x00007ff9909f1000 	D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\server\jvm.dll
0x00007ffa1c7a0000 - 0x00007ffa1c851000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffa1d5a0000 - 0x00007ffa1d648000 	C:\Windows\System32\sechost.dll
0x00007ffa1b9f0000 - 0x00007ffa1ba18000 	C:\Windows\System32\bcrypt.dll
0x00007ffa1d480000 - 0x00007ffa1d594000 	C:\Windows\System32\RPCRT4.dll
0x00007ffa1dd90000 - 0x00007ffa1de01000 	C:\Windows\System32\WS2_32.dll
0x00007ffa150b0000 - 0x00007ffa150e4000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffa1b360000 - 0x00007ffa1b3ad000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffa152e0000 - 0x00007ffa152ea000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffa1b340000 - 0x00007ffa1b353000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffa1a630000 - 0x00007ffa1a648000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffa0a3a0000 - 0x00007ffa0a3aa000 	D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\jimage.dll
0x00007ffa18d60000 - 0x00007ffa18f93000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffa1d9e0000 - 0x00007ffa1dd73000 	C:\Windows\System32\combase.dll
0x00007ffa1d6e0000 - 0x00007ffa1d7b7000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffa0abe0000 - 0x00007ffa0ac12000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffa1c0a0000 - 0x00007ffa1c11b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffa037c0000 - 0x00007ffa037e0000 	D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.5547_none_27104afb73855772;D:\software\IntelliJ IDEA 2025.1.1.1\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'http://***********:8929': 
java_class_path (initial): D:/software/IntelliJ IDEA 2025.1.1.1/plugins/vcs-git/lib/git4idea-rt.jar;D:/software/IntelliJ IDEA 2025.1.1.1/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 465567744                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 7436500992                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 7436500992                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_291
PATH=C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\libexec\git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Python312\Scripts;C:\Python312;D:\software\IntelliJ IDEA 2025.1.1.1\plugins\maven\lib\maven3\bin;D:\Program Files (x86)\VMware\VMware Workstation\bin;D:\software\python3.9\Scripts;D:\software\python3.9;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk1.8.0_291\bin;D:\software\NetSarang\Xmanager 7;D:\software\NetSarang\Xshell 7;D:\software\NetSarang\Xftp 7;D:\software\NetSarang\Xlpd 7;C:\Program Files\MySQL\MySQL Server 8.0\bin;D:\software;C:\ProgramData\chocolatey\bin;C:\Program Files\dotnet;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\CursorModifier;D:\software\pcsuite;D:\software\blender.exe;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;D:\software\IntelliJ IDEA 2024.1.4\plugins\maven\lib\maven3\bin;C:\Program Files\PowerShell\7;C:\Program Files (x86)\Microsoft\Edge\Application;D:\software\python3.9\Scripts;D:\software\python3.9;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files\Git\cmd;C:\Program Files\Java\jdk1.8.0_291\bin;D:\software\NetSarang\Xmanager 7;D:\software\NetSarang\Xshell 7;D:\software\NetSarang\Xftp 7;D:\software\NetSarang\Xlpd 7;C:\Program Files\MySQL\MySQL Server 8.0\bin;D:\software;C:\ProgramData\chocolatey\bin;C:\Program Files\dotnet;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\CursorModifier;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\software\IntelliJ IDEA 2024.1.4\bin;D:\software\W;D:\software\IntelliJ IDEA 2025.1.1.1\bin;D:\software\cursor\resources\app\bin
USERNAME=78669
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 116 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 12212K (0% of 29037428K total physical memory with 3643904K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.5415)
OS uptime: 2 days 2:18 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 25 model 116 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ss, avx512_ifma
Processor Information for processor 0
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 1
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 2
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 3
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 4
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801
Processor Information for processor 5
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 6
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 7
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 8
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801
Processor Information for processor 9
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 10
  Max Mhz: 3801, Current Mhz: 3801, Mhz Limit: 3801
Processor Information for processor 11
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 12
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 13
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 14
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801
Processor Information for processor 15
  Max Mhz: 3801, Current Mhz: 3040, Mhz Limit: 3801

Memory: 4k page, system-wide physical 28356M (3558M free)
TotalPageFile size 53956M (AvailPageFile size 372M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 71M, peak: 514M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109) for windows-amd64 JRE (21.0.6+9-b895.109), built on 2025-03-26 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
