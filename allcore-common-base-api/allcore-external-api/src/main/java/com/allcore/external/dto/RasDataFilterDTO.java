package com.allcore.external.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 14 7月 2025
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RasDataFilterDTO implements Serializable {

  private static final long serialVersionUID = -11566224164657691L;

  private String attr;

  private String comparator;

  private Object value;
}
