package com.allcore.external.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>RAS平台设备台账数据传输对象</p>
 *
 * @author: sunkun
 * Date: 14 7月 2025
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "RAS平台设备台账数据传输对象")
public class RasDataByObjDTO implements Serializable {

    private static final long serialVersionUID = -2522165876863221231L;

    /**
     * 对象编号
     */
    @ApiModelProperty(value = "对象编号")
    private String objId;

    /**
     * 查询属性
     */
    @ApiModelProperty(value = "查询属性")
    private List<String> attrs;

    /**
     * 页码
     */
    @ApiModelProperty(value = "页码")
    private Integer page;

    /**
     * 页大小
     */
    @ApiModelProperty(value = "页大小")
    private Integer pageSize;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 查询周期
     * H->时
     * D->日
     * W->周
     * M->月
     * Q->季
     * Y->年
     * 自定义
     */
    @ApiModelProperty(value = "查询周期")
    private String cycleType;

    /**
     * 统计类别
     */
    @ApiModelProperty(value = "统计类别")
    private String groupType;

    /**
     * 排序参数
     */
    @ApiModelProperty(value = "排序参数")
    private List<String> sorts;

    /**
     * 筛选参数
     * [{"attr":"skey","comparator":"in","value":[1,2,3]}]
     * comparator: =、>、<、>=、<=、like、in
     */
    @ApiModelProperty(value = "筛选参数")
    private List<RasDataFilterDTO> filters;
}
