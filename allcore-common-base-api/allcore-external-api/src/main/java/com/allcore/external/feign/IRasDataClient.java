package com.allcore.external.feign;

import com.allcore.external.dto.RasDataByObjDTO;
import com.allcore.external.dto.RasDataBySKeyDTO;
import com.allcore.external.vo.RasDataVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 14 7月 2025
 */
@FeignClient(name = "RAS", url = "${ras.httpHost}", path = "/unifiedData/data/config")
//@FeignClient(name = "RAS", url = "http://*************", path = "/ras/data")
public interface IRasDataClient {

    @PostMapping("/dataByObj")
    RasDataVO dataByObj(RasDataByObjDTO dto);

    @PostMapping("/dataBySkey")
    RasDataVO dataBySkey(RasDataBySKeyDTO dto);

}
