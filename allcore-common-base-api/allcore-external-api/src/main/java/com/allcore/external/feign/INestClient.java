package com.allcore.external.feign;

import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.tool.api.R;
import com.allcore.external.dto.NestTaskDTO;
import com.allcore.external.dto.ReceivePicNewDTO;
import com.allcore.external.entity.Nest;
import com.allcore.external.entity.NestTask;
import com.allcore.external.entity.NestTaskDetail;
import com.allcore.external.entity.NestTaskWithDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 消息 Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
        value = LauncherConstant.MACHINE_NEST_SERVER_NAME, fallback = INestClientBack.class
)
public interface INestClient {

    String API_PREFIX = "/client";
    String SAVE_BATCH = API_PREFIX + "/saveBatch";
    String SAVE_DETAIL_BATCH = API_PREFIX + "/saveDetailBatch";
    String SAVE_RECEIVE_PIC = API_PREFIX + "/saveReceivePic";
    String GET_NEST_TASK = API_PREFIX + "/getNestTask";
    String GET_NEST_TASK_DETAIL = API_PREFIX + "/getNestTaskDetail";
    String sendTaskToMachine = API_PREFIX + "/sendTaskToMachine";
    String EXCUTE_MISSION = API_PREFIX + "/excuteMission";
    String WEATHER_WARNING=API_PREFIX+"/weatherWarning";
    String GET_PV_STATISTICS = API_PREFIX + "/getPvStatistics";

    /**
     *
     */
    @PostMapping(SAVE_BATCH)
    R saveBatch(@RequestBody List<NestTaskDTO> nestTaskDTOList);

    @PostMapping(SAVE_DETAIL_BATCH)
    R saveDetailBatch(@RequestBody List<NestTaskDetail> nestTaskDetailList);

    @GetMapping(GET_NEST_TASK)
    R<NestTask> getNestTask(@RequestParam String airportNestId);

    @GetMapping(GET_NEST_TASK_DETAIL)
    R<NestTaskDetail> getNestTaskDetail(@RequestParam String airportNestId);


    @GetMapping(sendTaskToMachine)
    void sendTaskToMachine(@RequestBody List<NestTaskDTO> nestTaskDTOList);


    @PostMapping(SAVE_RECEIVE_PIC)
    R saveReceivePic(@RequestBody ReceivePicNewDTO dto);

    @GetMapping(EXCUTE_MISSION)
    R excuteMission(@RequestParam String inspectionTaskId);

    @GetMapping(WEATHER_WARNING)
    R checkNestWeatherWarning(@RequestParam String siteId);

    @GetMapping(GET_PV_STATISTICS)
    R getPvStatistics();
}
