package com.allcore.external.feign;


import com.allcore.core.tool.api.R;
import com.allcore.external.dto.NestTaskDTO;
import com.allcore.external.dto.ReceivePicNewDTO;
import com.allcore.external.entity.NestTask;
import com.allcore.external.entity.NestTaskDetail;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/9/7 13:47
 */
@Component
public class INestClientBack implements INestClient {

    @Override
    public R saveBatch(List<NestTaskDTO> nestTaskDTOList) {
        return R.fail("获取数据失败");
    }

    @Override
    public R saveDetailBatch(List<NestTaskDetail> nestTaskDetailList) {
        return R.fail("获取数据失败");
    }

    @Override
    public R<NestTask> getNestTask(String airportNestId) {
        return R.fail("获取数据失败");
    }

    @Override
    public R<NestTaskDetail> getNestTaskDetail(String airportNestId) {
        return R.fail("获取数据失败");
    }

    @Override
    public void sendTaskToMachine(List<NestTaskDTO> nestTaskDTOList) {
    }

    @Override
    public R saveReceivePic(ReceivePicNewDTO dto) {
        return R.fail("获取数据失败");
    }

    @Override
    public R excuteMission(String inspectionTaskId) {
        return R.fail("获取数据失败");
    }

    @Override
    public R checkNestWeatherWarning(String siteId) {
        return R.fail("获取数据失败");
    }

    @Override
    public R getPvStatistics() {
        return R.fail("获取数据失败");
    }
}
