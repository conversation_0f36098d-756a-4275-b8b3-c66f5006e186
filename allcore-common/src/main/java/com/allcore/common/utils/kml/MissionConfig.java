package com.allcore.common.utils.kml;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

@Data
@XStreamAlias("wpml:missionConfig")
public class MissionConfig {

    /**
     * 飞向首航点模式
     */
    @XStreamAlias("wpml:flyToWaylineMode")
    private String flyToWaylineMode;

    /**
     * 航线结束动作
     */
    @XStreamAlias("wpml:finishAction")
    private String finishAction;

    /**
     * 失控是否继续执行航线
     * goContinue：继续执行航线
     * executeLostAction：退出航线，执行失控动作
     */
    @XStreamAlias("wpml:exitOnRCLost")
    private String exitOnRCLost;

    /**
     * 失控动作类型
     * goBack：返航。飞行器从失控位置飞向起飞点
     * landing：降落。飞行器从失控位置原地降落
     * hover：悬停。飞行器从失控位置悬停
     */
    @XStreamAlias("wpml:executeRCLostAction")
    private String executeRCLostAction;

    /**
     * 安全起飞高度
     */
    @XStreamAlias("wpml:takeOffSecurityHeight")
    private String takeOffSecurityHeight;

    /**
     * 全局航线过渡速度
     */
    @XStreamAlias("wpml:globalTransitionalSpeed")
    private String globalTransitionalSpeed;

    /**
     * 参考起飞点
     */
    @XStreamAlias("wpml:takeOffRefPoint")
    private String takeOffRefPoint;

    /**
     * 参考起飞点海拔高度
     */
    @XStreamAlias("wpml:takeOffRefPointAGLHeight")
    private String takeOffRefPointAGLHeight;


    @XStreamAlias("wpml:globalRTHHeight")
    private String globalRTHHeight;

    /**
     * 飞行器机型信息
     */
    @XStreamAlias("wpml:droneInfo")
    private DroneInfo droneInfo;

    /**
     * 负载机型信息
     */
    @XStreamAlias("wpml:payloadInfo")
    private PayloadInfo payloadInfo;
}