package com.allcore.common.utils.wpml.actionActuatorFuncParam;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Data;

/**
 * 动作参数(开始录像)
 *
 * <AUTHOR>
 * @date 2023/3/10 10:05
 * @version 1.0
 */
@Data
public class StartRecord extends WpmlActionActuatorFuncParam{
    /**
     * 拍摄照片文件后缀,为生成媒体文件命名时将额外附带该后缀。
     */
    @XStreamAlias("wpml:fileSuffix")
    private String fileSuffix;

    /**
     * 拍摄照片存储类型
     * zoom: 存储变焦镜头拍摄照片
     * wide: 存储广角镜头拍摄照片
     * ir: 存储红外镜头拍摄照片
     * narrow_band: 存储窄带镜头拍摄照片
     * 注：存储多个镜头照片，格式如“<wpml:payloadLensIndex>wide,ir,narrow_band</wpml:payloadLensIndex>”表示同时使用广角、红外和窄带镜头
     */
    @XStreamAlias("wpml:payloadLensIndex")
    private String payloadLensIndex;

    /**
     * 是否使用全局存储类型
     * 0：不使用全局设置
     * 1：使用全局设置
     */
    @XStreamAlias("wpml:useGlobalPayloadLensIndex")
    private Integer useGlobalPayloadLensIndex;
}
