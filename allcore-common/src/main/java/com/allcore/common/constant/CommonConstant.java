package com.allcore.common.constant;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface CommonConstant {
    String TEMPPATH = System.getProperty("user.dir") + "/tmp/";

    /**
     * 作业性质
     */
    String INSPECTION_TYPE = "inspection_type";

    /**
     * sword 系统名
     */
    String SWORD_NAME = "sword";

    /**
     * saber 系统名
     */
    String SABER_NAME = "saber";

    /**
     * allcore 系统client_id名
     */
    String ALL_CORE = "allcore";

    /**
     * 顶级父节点id
     */
    Integer TOP_PARENT_ID = 0;

    /**
     * 顶级父节点名称
     */
    String TOP_PARENT_NAME = "顶级";

    /**
     * 0
     */
    String ZERO_NUM = "0";
    String YES = "是";
    String NO = "否";

    /**
     * 1
     */
    String ONE_NUM = "1";

    /**
     * 2
     */
    String TWO_NUM = "2";

    String THREE_NUM = "3";

    String FOUR_NUM = "4";

    /**
     * 5
     */
    String FIVE_NUM = "5";

    String SIX_NUM = "6";

    String SEVEN_NUM = "7";

    String EIGHT_NUM = "8";

    String THREE_THOUSAND_SIX_HUNDRED_NUM = "3600";

    String ONE_THOUSAND = "1000";

    Integer MINUS_ONE = -1;

    /**
     * 0.0003
     */
    Double DOUBLE_RADIUS = 0.0003;

    /**
     * 空域信息展示 - 圆圈半径, 单位米
     */
    Double RADIUS = 200.0;

    /**
     * 数值0
     */
    Integer INTEGER_NUM_ZERO = 0;

    /**
     * 数值1
     */
    Integer INTEGER_NUM_ONE = 1;

    /**
     * 数值2
     */
    Integer INTEGER_NUM_TWO = 2;

    /**
     * 数值3
     */
    Integer INTEGER_NUM_THREE = 3;

    /**
     * 数值4
     */
    Integer INTEGER_NUM_FOUR = 4;
    /**
     * 数值6
     */
    Integer INTEGER_NUM_SIX = 6;
    /**
     * 数值7
     */
    Integer INTEGER_NUM_SEVEN = 7;
    /**
     * 数值9
     */
    Integer INTEGER_NUM_NINE = 9;
    /**
     * 数值10
     */
    Integer INTEGER_NUM_TEN = 10;
    /**
     * 数值30
     */
    Integer INTEGER_NUM_THIRTY = 30;
    /**
     * 数值100
     */
    Integer INTEGER_NUM_ONE_HUNDRED = 100;
    /**
     * 数值200
     */
    Integer INTEGER_NUM_TWO_HUNDRED = 200;
    /**
     * 数值20
     */
    Integer INTEGER_NUM_TWENTY = 20;
    /**
     * 数值280
     */
    Integer INTEGER_NUM_TWO_HUNDRED_EIGHTY = 280;
    /**
     * 数值300
     */
    Integer INTEGER_NUM_THREE_HUNDRED = 300;
    /**
     * 数值385
     */
    Integer INTEGER_NUM_THREE_HUNDRED_EIGHTY_FIVE = 385;
    /**
     * 数值1024
     */
    Integer INTEGER_NUM_1024 = 1024;
    /**
     * 数值1024
     */
    Integer INTEGER_NUM_9999 = 9999;
    /**
     * 默认密码
     */
    String DEFAULT_PASSWORD = "Jsepc01!";

    String DEFAULT_INDEX_FLAG = "default";

    String ISC_INDEX_FLAG = "isc";

    /**
     * 基础数据类型 整型
     */
    String DATA_TYPE_INTEGER = "java.lang.Integer";

    /**
     * String
     */
    String DATA_TYPE_STRING = "java.lang.String";

    /**
     * 基础数据类型 Double
     */
    String DATA_TYPE_DOUBLE = "java.lang.Double";

    /**
     * 基础数据类型 Float
     */
    String DATA_TYPE_FLOAT = "java.lang.Float";

    /**
     * 基础数据类型 Long
     */
    String DATA_TYPE_LONG = "java.lang.Long";

    /**
     * 基础数据类型 Short
     */
    String DATA_TYPE_SHORT = "java.lang.Short";

    /**
     * 基础数据类型 Byte
     */
    String DATA_TYPE_BYTE = "java.lang.Byte";

    /**
     * 基础数据类型 Char
     */
    String DATA_TYPE_CHARACTER = "java.lang.Character";

    /**
     * 基础数据类型 Boolean
     */
    String DATA_TYPE_BOOLEAN = "java.lang.Boolean";

    /**
     * 日期类型 Date
     */
    String DATA_TYPE_DATE = "java.util.Date";

    /**
     * xls
     */
    String XLS_FORMAT = "xls";

    /**
     * xlsx
     */
    String XLSX_FORMAT = "xlsx";

    /**
     * 分页限制条数
     * 
     * <AUTHOR>
     * @date 2022/9/1 20:17
     */
    String SQL_LIMIT = "limit 1";

    /**
     * AES秘钥
     */
    String AES_SECRETKEY = "uav-aes-secret-key";

    /**
     * 无人机设备编号
     */
    String EQUIPMENT_NO = "equipmentNo";
    /**
     * zip
     */
    String ZIP_FORMAT = "zip";

    /**
     * json
     */
    String JSON_FORMAT = "json";

    /**
     * png
     */
    String PNG_FORMAT = "png";

    /**
     * pdf
     */
    String PDF_FORMAT = "pdf";

    /**
     * iso-8859-1
     */
    String ISO_FORMAT = "iso-8859-1";

    /**
     * UTF-8
     */
    String UTF_EIGHT_FORMAT = "UTF-8";

    String VERSION_V1 = "v1";

    String SUCCESS = "success";

    String WGGD = "wggd";

    /**
     * 默认密码参数值
     */
    String DEFAULT_PARAM_PASSWORD = "account.initPassword";

    /**
     * 数据权限类型
     */
    Integer DATA_SCOPE_CATEGORY = 1;

    /**
     * 接口权限类型
     */
    Integer API_SCOPE_CATEGORY = 2;
    String FAN_TEMPLATE_NAME = "风机模板";

    String FAN_TEMPLATE_PATH = "templates/source/fanTemplate.xlsx";

    String FAN_BOX_TEMPLATE_NAME = "风机箱变模板";

    String FAN_BOX_TEMPLATE_PATH = "templates/source/fanBoxTemplate.xlsx";

    String AREA_TEMPLATE_NAME = "光伏区域模板";

    String AREA_TEMPLATE_PATH = "templates/source/areaTemplate.xlsx";

    String STRING_TEMPLATE_NAME = "光伏组串模板";

    String STRING_TEMPLATE_PATH = "templates/source/stringTemplate.xlsx";

    String COMPONENT_TEMPLATE_NAME = "光伏组件模板";

    String COMPONENT_TEMPLATE_PATH = "templates/source/componentTemplate.xlsx";

    String LINE_TEMPLATE_NAME = "线路模板";

    String LINE_TEMPLATE_PATH = "templates/source/lineTemplate.xlsx";

    String TOWER_TEMPLATE_NAME = "杆塔模板";

    String TOWER_TEMPLATE_PATH = "templates/source/towerTemplate.xlsx";

    String BOOSTER_TEMPLATE_NAME = "升压站模板";

    String BOOSTER_TEMPLATE_PATH = "templates/source/boosterTemplate.xlsx";

    String ENERGYSTORAGE_TEMPLATE_NAME = "储能站模板";

    String ENERGYSTORAGE_TEMPLATE_PATH = "templates/source/energyStorageTemplate.xlsx";

    String BOX_TEMPLATE_NAME = "光伏箱变模板";

    String BOX_TEMPLATE_PATH = "templates/source/boxTemplate.xlsx";

    String INVERTER_TEMPLATE_NAME = "光伏逆变器模板";

    String INVERTER_TEMPLATE_PATH = "templates/source/inverterTemplate.xlsx";

    /**
     * "E "
     */
    String STRING_E = "E ";
    /**
     * "N "
     */
    String STRING_N = "N ";


    /**
     * 第三方秘钥
     */
    String THIRD_PARTY_ASE_KEY = "third-party-ase-key";

    /**
     * 在飞无人机超时时间 5分钟
     */
    int VALID_CACHE_TIME = 60 * 5;
    int VALID_CACHE_TIME_ONE = 60;

    /**
     * 在飞无人机超时时间 5分钟
     */
    Long VALID_CACHE_TIMES = 300L;

    /**
     * APP服务的redis存储文件夹名
     */
    String APP_REDIS = "app:";

    String AIRPORT_REDIS = "airport:taskOnLine";

    /**
     * 消缺App
     */
    String DAY_SUM = "daySum";
    String TOTAL_SUM = "totalSum";
    String CAPACITY = "capacity";

}
