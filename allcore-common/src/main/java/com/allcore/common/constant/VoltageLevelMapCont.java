package com.allcore.common.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/12/16
 */
public class VoltageLevelMapCont {

    public final static Map<String, String > VOLTAGELEVEL_MAP= new HashMap<String, String>() {
        {
            put("07","交流220V");
            put("08","交流380V(含400V)");
            put("09","交流660V");
            put("10","交流1000V（含1140V）");
            put("11","交流600V");
            put("12","交流750V");
            put("13","交流1500V");
            put("14","交流3000V");
            put("15","交流2500V");
            put("20","交流3kV");
            put("21","交流6kV");
            put("22","交流10kV");
            put("23","交流15.75kV");
            put("24","交流20kV");
            put("25","交流35kV");
            put("30","交流66kV");
            put("31","交流72.5kV");
            put("32","交流110kV");
            put("33","交流220kV");
            put("34","交流330kV");
            put("35","交流500kV");
            put("36","交流750kV");
            put("37","交流1000kV");
            put("51","直流6V");
            put("52","直流12V");
            put("53","直流24V");
            put("54","直流36V");
            put("55","直流48V");
            put("56","直流110V");
            put("60","直流220V");
            put("70","直流600V");
            put("71","直流750V");
            put("72","直流1500V");
            put("73","直流3000V");
            put("76","直流35kV");
            put("77","直流30kV");
            put("78","直流50kV");
            put("80","直流120kV");
            put("81","直流125kV");
            put("82","直流400kV");
            put("83","直流500kV");
            put("84","直流660kV");
            put("85","直流800kV");
            put("86","直流1000kV");
            put("87","直流200kV");
            put("88","直流320kV");
            put("90","直流166.7kV");
            put("99","无电源");
        }
    };
}
