package com.allcore.common.constant;


import static com.allcore.core.launch.constant.AppConstant.APPLICATION_NAME_PREFIX;

import com.allcore.core.launch.constant.AppConstant;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface LauncherConstant {

	/**
	 * 门户 服务
	 */
	String PORTAL_SERVER_NAME = "portal-server";
	/**
	 * 主服务
	 */
	String MAIN_SERVER_NAME = "allcore-main";
	String APP_SERVER_NAME = "allcore-app";
	String STATISTIC_SERVER_NAME = "allcore-statistic";
	String MACHINE_NEST_SERVER_NAME = "allcore-external";

	/**
	 * i6000服务 端口号到18101了
	 */
	String I6000_SERVER_NAME = "i6000-server";

	/**
	 * 同步服务
	 */
	String SYNC_SERVER_NAME = "sync-server";

	/**
	 * 规则服务
	 */
	String RULE_SERVER_NAME = "rule-server";

	/**
	 * 文件服务
	 */
	String FILE_RESOURCE_NAME = "file-server";

	/** 缺陷服务
	 */
	String DEFECT_RESOURCE_NAME = "defect-server";

	/**

	/**
 * 空域服务
	 */
	String AIRSPACE_SERVER_NAME = "airspace-server";

	/**
	 * app服务
	 */
	String APPLICATION_APP_NAME = "app-server";


	/**
	 * 基础服务
	 */
	String BASIC_RESOURCE_NAME = "basic-server";

	/**
	 * 三维服务
	 */
	String THREE_SERVER_NAME = "three-server";

	/**
	 * 服务
	 */
	String MAIN_RESOURCE_NAME = "main-server";
	/**
	 * 定时服务
	 */
	String SCHEDULE_RESOURCE_NAME = "schedule-server";

	/**
	 * 网格化服务
	 */
	String GRID_SERVER_NAME = "grid-server";

	/**
	 * 统计服务
	 */
	String STATISTICS_SERVER_NAME = "statistics-server";

	/**
	 * 统计服务
	 */
	String ALLCORE_JOB_NAME = "allcore-job";

	/**	 * nacos namespace id
	 */
	String NACOS_NAMESPACE = "f447a694-519a-4255-95f9-bcbb5a5d6369";

	/**
	 * nacos dev 地址
	 */
	String NACOS_DEV_ADDR = "127.0.0.1:8850";
	//现场
//	String NACOS_DEV_ADDR = "100.108.1.10:18848";

	/**
	 * nacos prod 地址
	 */
	String NACOS_PROD_ADDR = "172.30.0.48:8848";

	/**
	 * nacos test 地址
	 */
	String NACOS_TEST_ADDR = "10.10.1.103:8848";

	/**
	 * sentinel dev 地址
	 */
	String SENTINEL_DEV_ADDR = "10.10.10.109:8858";

	/**
	 * sentinel prod 地址
	 */
	String SENTINEL_PROD_ADDR = "172.30.0.68:8858";

	/**
	 * sentinel test 地址
	 */
	String SENTINEL_TEST_ADDR = "172.30.0.58:8858";

	/**
	 * seata dev 地址
	 */
	String SEATA_DEV_ADDR = "10.10.10.109:8091";

	/**
	 * seata prod 地址
	 */
	String SEATA_PROD_ADDR = "172.30.0.68:8091";

	/**
	 * seata test 地址
	 */
	String SEATA_TEST_ADDR = "172.30.0.68:8091";

	String NACOS_DEV_USER = "nacos";

	String NACOS_TEST_USER = "nacos";

	String NACOS_PROD_USER = "";

	String NACOS_DEV_PASSWORD = "nacos";

	String NACOS_TEST_PASSWORD = "Zxhc@1234ns";

	String NACOS_PROD_PASSWORD = "";

	/**
	 * dbuuo提供者
	 */
	String APPLICATION_DUBBO_PROVIDER_NAME = APPLICATION_NAME_PREFIX + "dubbo-provider";

	/**
	 * dbuuo消费者
	 */
	String APPLICATION_DUBBO_CONSUMER_NAME = APPLICATION_NAME_PREFIX + "dubbo-consumer";

	/**
	 * seata订单
	 */
	String APPLICATION_SEATA_ORDER_NAME = APPLICATION_NAME_PREFIX + "seata-order";

	/**
	 * seata库存
	 */
	String APPLICATION_SEATA_STORAGE_NAME = APPLICATION_NAME_PREFIX + "seata-storage";

	/**
	 * easypoi
	 */
	String APPLICATION_EASYPOI_NAME = APPLICATION_NAME_PREFIX + "easypoi";

	/**
	 * kafka
	 */
	String APPLICATION_KAFKA_NAME = APPLICATION_NAME_PREFIX + "kafka";

	/**
	 * rabbit
	 */
	String APPLICATION_RABBIT_NAME = APPLICATION_NAME_PREFIX + "rabbit";

	/**
	 * stream消费者
	 */
	String APPLICATION_STREAM_CONSUMER_NAME = APPLICATION_NAME_PREFIX + "stream-consumer";

	/**
	 * stream生产者
	 */
	String APPLICATION_STREAM_PROVIDER_NAME = APPLICATION_NAME_PREFIX + "stream-provider";

	/**
	 * seata file模式
	 */
	String FILE_MODE = "file";

	/**
	 * seata nacos模式
	 */
	String NACOS_MODE = "nacos";

	/**
	 * seata default模式
	 */
	String DEFAULT_MODE = "default";

	/**
	 * seata group后缀
	 */
	String GROUP_NAME = "-group";


	//默认空public
//	String NACOS_DEV_NAMESPACE = "71c70b95-6561-4e6b-ac52-eb27f85c9409";
//	String NACOS_DEV_NAMESPACE = "942f2676-2af5-4d30-a1fc-644b2a7d6631";

	// 现场
//	String NACOS_DEV_NAMESPACE = "89df61a0-a3e1-4d6a-89d4-1d1c99974451";
	// 现场测试
	String NACOS_DEV_NAMESPACE = "ddca1559-3bfd-4ffc-9a03-b43df4eb6549";

	String NACOS_TEST_NAMESPACE = "942f2676-2af5-4d30-a1fc-644b2a7d6631";

	String NACOS_PROD_NAMESPACE = "";


	/**
	 * seata 服务组格式
	 *
	 * @param appName 服务名
	 * @return group
	 */
	static String seataServiceGroup(String appName) {
		return appName.concat(GROUP_NAME);
	}

	/**
	 * 动态获取nacos地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String nacosAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return NACOS_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return NACOS_TEST_ADDR;
			default:
				return NACOS_DEV_ADDR;
		}
	}

	/**
	 * 动态获取nacos_user
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String nacosUser(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return NACOS_PROD_USER;
			case (AppConstant.TEST_CODE):
				return NACOS_TEST_USER;
			default:
				return NACOS_DEV_USER;
		}
	}

	/**
	 * 动态获取nacos_password
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String nacosPassword(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return NACOS_PROD_PASSWORD;
			case (AppConstant.TEST_CODE):
				return NACOS_TEST_PASSWORD;
			default:
				return NACOS_DEV_PASSWORD;
		}
	}

	/**
	 * 动态获取sentinel地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String sentinelAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return SENTINEL_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return SENTINEL_TEST_ADDR;
			default:
				return SENTINEL_DEV_ADDR;
		}
	}

	/**
	 * 动态获取seata地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String seataAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return SEATA_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return SEATA_TEST_ADDR;
			default:
				return SEATA_DEV_ADDR;
		}
	}


	/**
	 * 动态获取nacos命名空间
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String nacosNameSpace(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return NACOS_PROD_NAMESPACE;
			case (AppConstant.TEST_CODE):
				return NACOS_TEST_NAMESPACE;
			default:
				return NACOS_DEV_NAMESPACE;
		}
	}

}
