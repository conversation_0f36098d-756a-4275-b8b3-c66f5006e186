package com.allcore.common.enums;

import java.util.Arrays;

import org.apache.commons.lang3.StringUtils;

import lombok.Getter;

/**
 * 业务字典枚举
 *
 * <AUTHOR>
 * @date 2022/04/21 14:59
 **/
@Getter
public enum BizDictEnum implements AllCoreCodeEnum {

	DIAGNOSE_TYPE("diagnose_type","诊断类型"),
	//光伏箱变
	BOX_OUTPUT_POWER_LOW("box_output_power_low","输出功率偏低"),
	//光伏逆变器
	COMMUNICATION_FAILURE("communication_failure","通信故障"),
	INVERTER_OUTPUT_POWER_LOW("inverter_output_power_low","输出功率偏低"),
	DISCRETE_RATE_HIGH("discrete_rate_high","离散率高"),
	POWER_LIMITATION("power_limitation","限功率"),
	//光伏组串
	STRING_ZERO_CURRENT("string_zero_current","组串零电流"),
	INEFFICIENT_STRINGING("inefficient_stringing","组串低效"),
	SHADOW_OCCLUSION("shadow_occlusion","阴影遮挡"),

	FAN_BIG_POSITION("fan_big_position", "风机缺陷大体的位置"),
	FAN_BLADE_ORDER("fan_blade_order", "风机叶片排序"),
	FAN_DETAIL_POSITION("fan_detail_position", "风机缺陷具体位置"),
	FAN_DETAIL_POSITION_QY("QY", "前缘"),
	FAN_DETAIL_POSITION_HY("HY", "后缘"),
	FAN_DETAIL_POSITION_YLM("YLM", "压力面"),
	FAN_DETAIL_POSITION_FYM("FYM", "负压面"),
	FAN_DETAIL_POSITION_JC("JC", "机舱"),
	BLADE("blade", "叶片"),
	TOWER("tower", "塔筒"),

	/**
	 * 资产属性
	 */
	ASSET_ATTRIBUTE("asset_attribute", "资产属性"),
	/**
	 * 资产属性-自有
	 */
	ASSET_ATTRIBUTE_OWN("1", "自有"),
	/**
	 * 资产属性-租赁
	 */
	ASSET_ATTRIBUTE_LEASE("2", "租赁"),
	/**
	 * 计划类型
	 */
	PLAN_TYPE("plan_type", "计划类型"),
	/**
	 * 计划类型-年度计划
	 */
	PLAN_TYPE_ANNUAL_PLAN("annualPlan", "年度计划"),
	/**
	 * 计划类型-月度计划
	 */
	PLAN_TYPE_MONTHLY_PLAN("monthlyPlan", "月度计划"),
	/**
	 * 计划类型-临时计划
	 */
	PLAN_TYPE_LUNAR_PLAN("lunarPlan", "临时计划"),

	/**
	 * 巡检作业性质
	 */
	INSPECTION_TYPE("inspection_type", "作业性质"),
	/**
	 * 巡检作业性质-线路巡检
	 */
	INSPECTION_TYPE_LINE("LINE", "线路巡检"),

	DEVICE_TOWER("TOWER", "杆塔"),
	/**
	 * 巡检作业性质-风机巡检
	 */
	INSPECTION_TYPE_FAN("FAN", "风机巡检"),
	/**
	 * 巡检作业性质-光伏巡检
	 */
	INSPECTION_TYPE_PV("PV", "光伏巡检"),
	/**
	 * 巡检作业性质-自主巡检
	 */
	INSPECTION_TYPE_AUTO("AUTO", "自主巡检"),
	/**
	 * 巡检作业性质-非自主巡检
	 */
	INSPECTION_TYPE_NONAUTO("NON-AUTO", "非自主巡检"),

	/**
	 * 设备类型
	 */
	DEVICE_TYPE("device_type", "设备类型"),

	/**
	 * 设备类型-二维码
	 */
	QRCODE_DEVICE_TYPE("qrcode_device_type","二维码设备类型"),

	/**
	 * 设备类型-输电线路
	 */
	DEVICE_TYPE_TMS_LINE("TMS_LINE", "输电线路"),

	/**
	 * 设备类型-配电线路
	 */
	DEVICE_TYPE_DMS_LINE("DMS_LINE", "配电线路"),

	/**
	 * 设备类型-输电杆塔
	 */
	DEVICE_TYPE_TMS_TOWER("TMS_TOWER", "输电杆塔"),

	/**
	 * 设备类型-配电杆塔
	 */
	DEVICE_TYPE_DMS_TOWER("DMS_TOWER", "配电杆塔"),

	/**
	 * 设备类型-风机
	 */
	DEVICE_TYPE_FAN("FAN", "风机"),
	DEVICE_TYPE_FAN_BOX("FAN_BOX", "风机箱变"),


	/**
	 * 设备类型-光伏
	 */
	DEVICE_TYPE_PV("PV", "光伏"),
	DEVICE_TYPE_PV_AREA("PV_AREA", "光伏区域"),
	DEVICE_TYPE_PV_STRING("PV_STRING", "光伏组串"),
	DEVICE_TYPE_PV_COMPONENTS("PV_COMPONENTS", "光伏组件"),
	DEVICE_TYPE_PV_BOX("PV_BOX","光伏箱变"),
	DEVICE_TYPE_PV_INVERTER("PV_INVERTER","光伏逆变器"),
	DEVICE_TYPE_PV_COMPONENT_MODEL("component_model","光伏组件型号"),
	/**
	 * 升压站
	 */
	DEVICE_TYPE_BOOSTER_STATION("BOOSTER", "升压站"),
	/**
	 * 线路设备
	 */
	DEVICE_TYPE_INTEGRATE_LINE("LINE", "集电线路"),

	DEVICE_TYPE_INTEGRATE_TOWER("TOWER", "杆塔"),
	/**
	 * 计划状态
	 */
	PLAN_STATUS("plan_status", "计划状态"),
	/**
	 * 计划状态-待上报
	 */
	PLAN_STATUS_TO_REPORT("1", "待上报"),
	/**
	 * 计划状态-待提交
	 */
	PLAN_STATUS_TO_SUBMIT("2", "待提交"),
	/**
	 * 计划状态-待审核
	 */
	PLAN_STATUS_TO_AUDIT("3", "待审核"),
	/**
	 * 计划状态-驳回待修改
	 */
	PLAN_STATUS_REJECT_TO_UPDATE("4", "驳回待修改"),
	/**
	 * 计划状态-终止
	 */
	PLAN_STATUS_TERMINATION("5", "终止"),
	/**
	 * 计划状态-待执行
	 */
	PLAN_STATUS_TO_EXECUTE("6", "待执行"),
	/**
	 * 计划状态-执行中
	 */
	PLAN_STATUS_EXECUTING("7", "执行中"),
	/**
	 * 计划状态-已完成
	 */
	PLAN_STATUS_FINISHED("8", "已完成"),

	/**
	 * 无人机类型
	 */
	UAV_TYPE("uav_type", "无人机类型"),
	UAV_TYPE_BIG("1", "大型无人直升机"),
	UAV_TYPE_MID("2", "中型无人直升机"),
	UAV_TYPE_FIXD("3", "固定翼无人机"),
	UAV_TYPE_MANY("4", "多旋翼无人机"),
	UAV_TYPE_COMPOSITE("5", "复合翼无人机"),

	/**
	 * 无人机状态
	 */
	UAV_STATE("uav_state", "无人机状态"),
	UAV_STATE_ZERO("0", "正常"),
	UAV_STATE_ONE("1", "报丢"),
	UAV_STATE_TWO("2", "报损"),
	UAV_STATE_THREE("3", "报废"),

	/**
	 * 制造厂商
	 */
	MANUFACTURER("manufacturer", "制造厂商"),

	/**
	 * 无人机品牌
	 */
	UAV_BRAND("uav_brand", "无人机品牌"),

	/**
	 * 设备来源
	 */
	DEVICE_SOURCE("device_source", "设备来源"),

	/**
	 * 缺陷等级
	 */
	DEFECT_LEVEL("defect_level", "缺陷等级"),
	DEFECT_LEVEL_NORMAL("normal", "一般"),
	DEFECT_LEVEL_URGENT("urgent", "重要"),
	DEFECT_LEVEL_BIG("big", "重大"),

	ALGORITHM_DEFECT_LEVEL("algorithm_defect_level", "算法缺陷等级"),
	ALGORITHM_DEFECT_LEVEL_NORMAL("0", "normal"),
	ALGORITHM_DEFECT_LEVEL_URGENT("1", "urgent"),
	ALGORITHM_DEFECT_LEVEL_BIG("2", "big"),

	/**
	 * 算法容器状态
	 */
	DOCKER_STATUS("docker_status", "算法容器状态"),
	DOCKER_STATUS_UNKNOW("unknow", "未识别"),
	DOCKER_STATUS_KNOW("know", "已识别"),

	/**
	 * 审核状态
	 */
	AUDIT_STATUS("audit_status", "审核状态"),
	AUDIT_STATUS_UNREVIEWED("unreviewed", "未审核"),
	AUDIT_STATUS_FAILED("audit_failed", "审核未通过"),
	AUDIT_STATUS_PASS("audit_pass", "审核通过"),

	/**
	 * 图片缺陷标识
	 */
	PIC_DEFECT_FLG("pic_defect_flg", "图片缺陷标识"),
	PIC_DEFECT_FLG_UNLABELED("unlabeled", "未标注图片"),
	PIC_DEFECT_FLG_NO_DEFECT("no_defect", "正常图片"),
	PIC_DEFECT_FLG_ALREADY("already_annotation", "已标注图片"),

	/**
	 * 缺陷识别任务状态
	 */
	RECOGNITION_TASK_STATUS("recognition_task_status", "缺陷识别任务状态"),
	RECOGNITION_STATUS_UNIDENTIFICATION("unidentification", "未识别"),
	RECOGNITION_STATUS_TO_IDENTIFY("to_identify_the", "算法识别中"),
	RECOGNITION_STATUS_USER_HANDLE("user_handle", "人工审核"),
	RECOGNITION_STATUS_IDENTIFIED("the_identified", "识别完成"),
	RECOGNITION_STATUS_IN_REVIEW("in_review", "审核中"),
	RECOGNITION_STATUS_RETURNED("returned", "已回退"),
	RECOGNITION_STATUS_COMPLETED("completed", "任务完成"),

	/**
	 * 识别任务类型
	 */
	TASK_TYPE("task_type", "缺陷识别任务状态"),
	TASK_TYPE_VOLUNTARILY("voluntarily", "算法"),
	TASK_TYPE_MANUAL_OPERATION("manual_operation", "人工"),

	/**
	 * 缺陷识别数据来源
	 */
	RECOGNITION_DATA_SOURCE("recognition_data_source", "缺陷识别数据来源"),
	RECOGNITION_SOURCE_WORK_ORDER("work_order", "工单"),
	RECOGNITION_SOURCE_OTHER("other", "其他"),

	/**
	 * 系统类型
	 */
	SYSTEM_TYPE("system_type", "系统类型"),
	SYSTEM_TYPE_WORK_ORDER("PD", "配电"),
	SYSTEM_TYPE_SOURCE("SD", "输电"),

	/**
	 * 消缺数据来源
	 */
	REMOVE_DATA_SOURCE("remove_data_source", "消缺数据来源"),
	REMOVE_SOURCE_DESIGN_TASK("design_task", "制定任务"),
	REMOVE_SOURCE_DEFECT_DISTINGUISH("defect_distinguish", "缺陷识别"),

	/**
	 * 消缺任务状态
	 */
	REMOVE_TASK_STATUS("remove_task_status", "消缺任务状态"),
	REMOVE_STATUS_TO_SEND("to_send", "待下发"),
	REMOVE_STATUS_NEW("new", "新增"),
	REMOVE_STATUS_ISSUED("issued", "已下发"),
	REMOVE_STATUS_IN_REVIEW("in_review", "审核中"),
	REMOVE_STATUS_RETURNED("returned", "已回退"),
	REMOVE_STATUS_DOING("doing", "执行中"),
	REMOVE_STATUS_COMPLETED("completed", "已完成"),

	/**
	 * 缺陷类型
	 */
	DEFECT_TYPE("defect_type", "缺陷类型"),
	DEFECT_TYPE_DEFECT("defect", "缺陷"),
	DEFECT_TYPE_PART("part", "部位"),

	/**
	 * 标注类型
	 */
	TAGGING_TYPE("tagging_type", "标注类型"),
	TAGGING_TYPE_RECTANGLE("rectangle", "长方形"),
	TAGGING_TYPE_LINE("line", "线"),

	/**
	 * 消缺状态
	 */
	ELIMINATE_STATUS("eliminate_status", "消缺状态"),
	TAGGING_TYPE_NOT_PUSH("not_push", "未推送"),
	TAGGING_TYPE_HAVE_TO_PUSH("have_to_push", "未消缺"),
	TAGGING_TYPE_ALREADY_VANISH_DEFECT("already_vanish_defect", "已消缺"),

	/**
	 * 消缺状态
	 */
    ELIMINATE_DEFECT_STATUS("eliminate_defect_status", "消缺状态"),
	/**
	 * 作业性质
	 */
	WORK_NATURE("work_nature", "作业性质"),
	WORK_NATURE_PEOPLE_PARTOL("people_partol", "人工巡检"),
	WORK_NATURE_FINE("fine_inspect", "精细化巡检"),
	WORK_NATURE_RED("red_inspect", "红外巡检"),
	WORK_NATURE_LASER("laser_inspect", "激光扫描"),
	WORK_NATURE_WAY("way_inspect", "通道巡检"),
	WORK_NATURE_TREE("tree_inspect", "树障巡检"),

	/**
	 * 流程引擎关联表名
	 */
	FLOW_TABLE("table_name", "流程引擎key"),
	FLOW_TABLE_ORDER("main_work_order_info", "巡检工单主表"),
	FLOW_TABLE_AIRSPACEAPPLY("main_airspace_apply", "空域申请主表"),
	FLOW_TABLE_INSPECTION_PLAN("main_inspection_plan_info", "巡检计划主表"),
	FLOW_TABLE_DEFECT("defect_recognition_task_info", "缺陷识别任务主表"),
	FLOW_TABLE_DEFECT_TICKET("defect_remove_work_ticket_info", "消缺工作票表主表"),
	FLOW_TABLE_WORK_TICKET("main_work_ticket_info", "巡检工作票主表"),
	/**
	 * 流程引擎key
	 */
	FLOW_KEY("flow_key", "流程引擎key"),
	FLOW_KEY_AIRSPACE("airspace", "空域申请"),
	FLOW_KEY_PLAN("plan", "巡检计划"),
	FLOW_KEY_ORDER("workorder", "巡检工单"),
	FLOW_KEY_DEFECT("defect", "缺陷审核"),
	FLOW_KEY_REMOVE("removetask", "消缺工作票审核"),
	FLOW_KEY_WORK_TICKET("workticket", "巡检工作票审核"),
	FLOW_KEY_REMOVE_TASKNOROLL_BACK("removeTaskNoRollBack", "消缺工作票变更及终结流程"),
	FLOW_KEY_WORK_TASKNOROLL_BACK("workTicketNoRollBack", "巡检工作票变更及终结流程"),

	/**
	 * 巡检工单申请状态
	 */
	ORDER_APPLY_STATUS("workOrder_status", "巡检工单申请状态"),
	ORDER_APPLY_STATUS_SB("1", "待上报"),
	ORDER_APPLY_STATUS_SH("2", "待审核"),
	ORDER_APPLY_STATUS_BS("3", "驳回待审核"),
	ORDER_APPLY_STATUS_ZS("4", "已终结"),
	ORDER_APPLY_STATUS_CP("5", "待持票"),
	ORDER_APPLY_STATUS_ZX("6", "持票执行"),
	ORDER_APPLY_STATUS_HC("7", "待确认"),
	ORDER_APPLY_STATUS_TJ("8", "待终结"),
	ORDER_APPLY_STATUS_DZJ("9", "已完成"),

	/**
	 * 缺陷流程状态
	 */
	FLOW_RECOGNITION_TASK_STATUS("recognition_task_status", "缺陷流程状态"),
	FLOW_RECOGNITION_STATUS_NOT_UPLOADED("1", "未上传"),
	FLOW_RECOGNITION_STATUS_UPLOADED("2", "已上传"),
	FLOW_RECOGNITION_STATUS_TO_SUBMIT("3", "待提交"),
	FLOW_RECOGNITION_STATUS_TO_REVIEW("4", "待审核"),
	FLOW_RECOGNITION_STATUS_RETURNED("5", "驳回修改"),
	FLOW_RECOGNITION_STATUS_TERMINATION("6", "终止"),
	FLOW_RECOGNITION_STATUS_COMPLETED("7", "已完成"),

	/**
	 * 空域性质
	 */
	AIRSPACE_TYPE("airspace_apply_type", "空域性质"),
	AIRSPACE_TYPE_TMP("1", "临时空域"),
	AIRSPACE_TYPE_YEAR("2", "年度空域"),

	FILE_STATUS("file_status", "批文状态"),

	/**
	 * 空域审核查询数据类型
	 */
	AIRSPACE_PAGELIST_DATA_ALL("1", "全部数据分页列表"),
	AIRSPACE_PAGELIST_DATA_AUDIT("2", "审核列表"),
	/**
	 * 空域信息查询数据类型
	 */
	AIRSPACE_SHOW_TYPE_ALL("0", "全部"),
	AIRSPACE_SHOW_TYPE_NOW("1", "当月有效"),
	AIRSPACE_SHOW_TYPE_NEXT("2", "下月失效"),
	/**
	 * 空域申请状态
	 */
	AIRSPACE_APPLY_STATUS("airspace_status", "空域申请状态"),
	AIRSPACE_APPLY_STATUS_TJ("0", "提交,用于判断用户当前动作是否为提交"),
	AIRSPACE_APPLY_STATUS_DT("1", "待提交"),
	AIRSPACE_APPLY_STATUS_DS("2", "待审核"),
	AIRSPACE_APPLY_STATUS_BH("3", "未通过"),
	AIRSPACE_APPLY_STATUS_TG("4", "已通过"),
	AIRSPACE_APPLY_STATUS_ZZ("5", "已终结"),
	/**
	 * 空域申请文件类型
	 */
	AIRSPACE_APPLY_FILE("file_type", "空域申请文件类型"),
	AIRSPACE_APPLY_FILE_IMG("1", "空域截图"),
	AIRSPACE_APPLY_FILE_SOUD("2", "电话录音"),

	/**
	 * 飞手人员性质
	 */
	OPERATOR_NATURE("operator_nature", "飞手人员性质"),
	/**
	 * 常用是否
	 */
	COMMON_YES_NO("common_yes_no", "常用是否"),
	COMMON_YES("yes", "是"),
	COMMON_NO("no", "否"),
	COMMON_WAIT("wait", "等待补全数据"),


	/**
	 * 缺陷图片类型
	 */
	DEFECT_PIC_TYPE("defect_pic_type", "缺陷图片类型"),
	DEFECT_PIC_TYPE_NORMAL("normal", "普通"),
	DEFECT_PIC_TYPE_UN_STRUCT("un_struct", "非结构化"),

	/**
	 * 三维
	 */
	THREE_UPLOAD_STATUS("three_upload_status", "上传状态"),
	THREE_FILE_TYPE("three_file_type", "三维文件类型"),
	THREE_FILE_TYPE_POINT_CLOUD("1", "激光点云"),
	THREE_FILE_TYPE_VERTICAL_IMAGE("2", "正摄影像"),
	THREE_FILE_TYPE_MODEL("3", "模型数据"),
	THREE_FILE_TYPE_VECTOR_MODEL("4", "矢量模型"),
	THREE_DATA_FACTORY("three_data_factory", "数据厂家"),
	THREE_FAN_TYPE("three_fan_type", "风机型号"),

	/**
	 * 意见反馈流转状态
	 */
	FEEDBACK_STATE("feedback_state", "意见反馈流转状态"),

	/**
	 * 航线类型
	 */
	ROUTE_TYPE("route_type", "航线类型"),

	/**
	 * 航线类型
	 */
	AGREEMENT_TYPE("agreement_type", "航迹协议类型"),

	/**
	 * 取证类型  1:取新证 2:复证
	 */
	LICENCE_CATEGORY("licence_category", "取证类型"),

	/**
	 * 证件类型  AOPA、UTC、CAAC、CEC、SGCC
	 */
	LICENCE_TYPE("licence_type", "证件类型"),
	LICENCE_TYPE_AOPA("AOPA", "无人机飞行操作资格证（AOPA)"),
	LICENCE_TYPE_UTC("UTC", "无人机飞行操作资格证（UTC)"),
	LICENCE_TYPE_SGCC("SGCC", "无人机飞行操作资格证（UTC)"),

	/**
	 * 1:多旋翼 2:固定翼 3:直升机
	 */
	CER_UAV_TYPE("cer_uav_type", "人员证书适用机型"),

	/**
	 * 1:Ⅰ级,2:Ⅱ级,3:Ⅲ级,4:IV级,5:V级,6:VI级
	 */
	CER_LEVEL("cer_level", "证书级别等级"),

	/**
	 * 1:初级工,2:中级工,3:高级工,4:技师,5:高级技师
	 */
	CER_SGCC_LEVEL("cer_sgcc_level", "sgcc证书级别等级"),

	/**
	 * 飞手审核状态
	 */
	OPERATOR_AUDIT_STATUS("operator_audit_status", "飞手审核状态"),
	OPERATOR_AUDIT_STATUS_START("start", "等待提交"),
	OPERATOR_AUDIT_STATUS_COMPLETE("complete", "审核完成"),


	/**
	 * 业务字典-节点类型
	 */
	NODE_TYPE("node_type", "节点类型"),

	/**
	 * 设备类型
	 */
	EQUIPMENT_TYPE("equipment_type", "设备类型"),

	/**
	 * 发证单位
	 */
	DOCUMENT_UNIT("document_unit", "发证单位"),
	UTC_DOCUMENT_UNIT("utc_document_unit", "utc证书发证单位"),
	AOPA_DOCUMENT_UNIT("aopa_document_unit", "aopa证书发证单位"),

	/**
	 * 1:视距内驾驶员,2:超视距驾驶员,3:教员
	 */
	DRIVER_LEVEL("driver_level", "驾驶员等级"),


	OPERATOR_IDENTITY("operator_identity","人员身份"),

	/**
	 * 消缺工作票类型
	 */
	DEFECT_REMOVE_WORK_TICKET_TYPE("defect_remove_work_ticket_type", "消缺工作票类型"),
	DEFECT_REMOVE_WORK_TICKET_TYPE_TWO("pv_ticket", "02-光伏票"),
	DEFECT_REMOVE_WORK_TICKET_TYPE_THREE("one_ticket", "03-一种票"),
	DEFECT_REMOVE_WORK_TICKET_TYPE_FOUR("two_ticket", "04-二种票"),
	DEFECT_REMOVE_WORK_TICKET_TYPE_FIVE("line_one_ticket", "05-线路一种票"),
	DEFECT_REMOVE_WORK_TICKET_TYPE_SIX("line_two_ticke", "06-线路二种票"),
	DEFECT_REMOVE_WORK_TICKET_TYPE_SEVEN("cable_one_ticket", "07-电缆一种票"),
	DEFECT_REMOVE_WORK_TICKET_TYPE_ELEVEN("three_ticket", "11-三种票"),

	/**
	 * 消缺工作票状态
	 */
	DEFECT_REMOVE_WORK_TICKET_STATE("defect_remove_work_ticket_state", "消缺工作票状态"),
	DEFECT_REMOVE_WORK_TICKET_STATE_ONE("1", "工作票审核"),
	DEFECT_REMOVE_WORK_TICKET_STATE_TWO("2", "变更审核"),
	DEFECT_REMOVE_WORK_TICKET_STATE_THREE("3", "工作票终结审核"),

	/**
	 * 消缺工作票审核状态
	 */
	DEFECT_REMOVE_WORK_TICKET_AUDIT_STATE("defect_remove_work_ticket_audit_state", "消缺工作票审核状态"),
	DEFECT_REMOVE_WORK_TICKET_AUDIT_STATE_ZERO("0", "待提交"),
	DEFECT_REMOVE_WORK_TICKET_AUDIT_STATE_ONE("1", "签发人待审核"),
	DEFECT_REMOVE_WORK_TICKET_AUDIT_STATE_TWO("2", "许可人待审核"),
	DEFECT_REMOVE_WORK_TICKET_AUDIT_STATE_THREE("3", "已通过"),
	DEFECT_REMOVE_WORK_TICKET_AUDIT_STATE_FOUR("4", "未通过"),
	TICKET_STATR("statr", "开工"),
	TICKET_END("end", "收工"),


	/**
	 * 巡检工作票--出勤记录类型
	 */
	MAIN_WORK_TICKET_RECORD_TYPE_START("1", "开工"),
	MAIN_WORK_TICKET_RECORD_TYPE_END("2", "收工"),
	/**
	 * 巡检工作票--签名照类型
	 */
	MAIN_WORK_TICKET_SIGNATURE_TYPE_ONE("1", "确认工作负责人布置的任务和本施工项目安全措施-工作班组人员签名"),
	MAIN_WORK_TICKET_SIGNATURE_TYPE_TWO("2", "危险点预控票-工作班组成员签字"),

	/**
	 * 巡检工作票状态
	 */
	MAIN_WORK_TICKET_STATE("main_work_ticket_review_type", "巡检工作票状态"),
	MAIN_WORK_TICKET_STATE_ONE("1", "工作票审核"),
	MAIN_WORK_TICKET_STATE_TWO("2", "变更审核"),
	MAIN_WORK_TICKET_STATE_THREE("3", "工作票终结审核"),

	/**
	 * 巡检工作票审核状态
	 */
	MAIN_WORK_TICKET_TICKET_AUDIT_STATE("main_work_ticket_review_state", "巡检工作票状态"),
	MAIN_WORK_TICKET_TICKET_AUDIT_STATE_ZERO("0", "待提交"),
	MAIN_WORK_TICKET_TICKET_AUDIT_STATE_ONE("1", "签发人待审核"),
	MAIN_WORK_TICKET_TICKET_AUDIT_STATE_TWO("2", "许可人待审核"),
	MAIN_WORK_TICKET_TICKET_AUDIT_STATE_THREE("3", "已通过"),
	MAIN_WORK_TICKET_TICKET_AUDIT_STATE_FOUR("4", "未通过"),


	/**
	 * 杆塔性质
	 */
	TOWER_PROPERTIES("tower_properties","杆塔性质"),

	/**
	 * 回路数
	 */
	LOOPS_NUMBER("loop_number","回路数"),

	/**
	 * 电压等级
	 */
	VOLATE_LEVEL("010401","电压等级"),
	VOLATE_LEVEL_TWENTY_TWO("22","交流10kV"),
	VOLATE_LEVEL_TWENTY_FIVE("25","交流35kV"),
	VOLATE_LEVEL_THIRTY_TWO("32","交流110kV"),
	VOLATE_LEVEL_THIRTY_THREE("33","交流220kV"),
	VOLATE_LEVEL_THIRTY_FIVE("35","交流500kV"),
	VOLATE_LEVEL_EIGHTY_FIVE("85","直流800kV"),

	/**
	 * 专业
	 */
	MAJOR("major","专业"),
	/**
	 * 航线类型
	 */
	INSPECTION_ROUTE_TYPE("inspection_type","航线类型"),
	/**
	 * 航线相别
	 */
	DIFFERENT_ROUTES("different_routes","航线相别"),
	/**
	 * 航线生成类型
	 */
	BUILD_TYPE("build_type","航线生成类型"),

	FLIGHT_CROSS_TYPE("flight_cross_type","飞行跨越类型"),
	ALGORITHM_CODE("algorithm_code","算法code对应关系"),
	DEFECT_CODE("defect_code","缺陷code"),
	DEFECT_CODE_FAN("defect_code_fan","风机缺陷code"),
	DEFECT_LEVEL_FAN("defect_level_fan","风机缺陷等级"),
	DEFECT_CODE_PV("defect_code_pv","光伏缺陷code"),
	DEFECT_CODE_TMS_TOWER("defect_code_tms_tower","输电缺陷code"),

	VIDEO_STATUS("video_status", "影像状态"),

	MACHINE_NEST_STATUS("machine_nest_status", "机巢状态"),

	ALARM_SOURCE("alarm_source", "告警来源"),
	ALARM_SOURCE_ELECTRONIC_FENCE("electronic_fence", "电子围栏"),
	ALARM_SOURCE_INTELLIGENT_ACCESS_CONTROL("intelligent_access_control", "智能门禁"),
	ALARM_SOURCE_TMS_LINE("tms_line", "输电告警"),
	ALARM_SOURCE_INTEGRATED_LINE("integrated_line", "集电线路告警"),
	ALARM_SOURCE_ORIBITAL_ROBOT("oribital_robot", "轨道机器人告警"),
	ALARM_SOURCE_SGC_ROBOT("sgc_robot", "操作机器人告警"),
	ALARM_SOURCE_DIAGNOSTIC_ALARM("diagnostic_alarm", "诊断告警"),
	ALARM_SOURCE_DEFECT_IDENTIFICATION("defect_identification", "缺陷识别")
	;
	/**
	 * 字典编码
	 */
	private final String code;
	/**
	 * 字典名称
	 */
	private final String name;

	/**
	 * 构造方法
	 *
	 * @param code 字典编码
	 * @param name 字典名称
	 */
	BizDictEnum(String code, String name) {
		this.code = code;
		this.name = name;
	}

	/**
	 * 根据name获取code
	 *
	 * @param name
	 * @return
	 */
	public static String getCodeByName(String name) {
		BizDictEnum[] enums = BizDictEnum.values();
		for (BizDictEnum item : enums) {
			if (item.getName().equals(name)) {
				return item.getCode();
			}
		}
		return null;
	}

	/**
	 * 根据code获取name
	 *
	 * @param code
	 * @return
	 */
	public static String getNameByCode(String code) {
		BizDictEnum[] enums = BizDictEnum.values();
		for (BizDictEnum item : enums) {
			if (item.getCode().equals(code)) {
				return item.getName();
			}
		}
		return null;
	}
	/**
	 * 根据code获取 BizDictEnum
	 * @param code 字典编码
	 * @return BizDictEnum
	 */
	public static BizDictEnum getEnumByCode(String code) {
		return Arrays.stream(values()).filter(bizDictEnum ->
						StringUtils.equals(code, bizDictEnum.getCode()))
				.findAny().orElse(null);
	}
}
