package com.allcore.filesystem.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.imaging.Imaging;
import org.apache.commons.imaging.formats.jpeg.JpegImageMetadata;
import org.apache.commons.imaging.formats.jpeg.exif.ExifRewriter;
import org.apache.commons.imaging.formats.tiff.TiffImageMetadata;
import org.apache.commons.imaging.formats.tiff.write.TiffOutputDirectory;
import org.apache.commons.imaging.formats.tiff.write.TiffOutputSet;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.io.*;
import java.util.List;

/**
 * <AUTHOR>
 * @title: PicUtils
 * @projectName MyData
 * @description:
 * @date 2021/7/20 17:30
 */
@Slf4j
public class PicUtil {

    public static Object  imageCompress() throws Exception {
        String filePath = "F:\\img";
        File file = new File(filePath);
        if(!file.isDirectory()){
            return null;
        }
        File[] files = file.listFiles();
        if(files == null){
            return null;
        }
        //设定图片压缩参数
        BufferedImage src = null;
        FileOutputStream out = null;
        ImageWriter imgWrier;
        ImageWriteParam imgWriteParams;
        File imageCompress = new File(filePath+"\\compress");
        if(imageCompress.exists()){
            if(deleteDir(imageCompress)){
                System.out.println("删除成功！");
            }else{
                System.out.println("删除失败！");
            }
        }
        imageCompress.mkdirs();
        for(int i = 0 ;i < files.length; i++){
            File f = files[i];
            if(f.isFile() && f.getName().substring(f.getName().length()-3).equalsIgnoreCase("jpg")){
                log.debug("开始设定压缩图片参数");
                // 指定写图片的方式为 jpg
                imgWrier = ImageIO.getImageWritersByFormatName("jpg").next();
                imgWriteParams = new javax.imageio.plugins.jpeg.JPEGImageWriteParam(
                        null);
                // 要使用压缩，必须指定压缩方式为MODE_EXPLICIT
                imgWriteParams.setCompressionMode(imgWriteParams.MODE_EXPLICIT);
                // 这里指定压缩的程度，参数qality是取值0~1范围内，
                imgWriteParams.setCompressionQuality(0.5F);
                imgWriteParams.setProgressiveMode(imgWriteParams.MODE_DISABLED);
                ColorModel colorModel =ImageIO.read(f).getColorModel();// ColorModel.getRGBdefault();
                // 指定压缩时使用的色彩模式
//                imgWriteParams.setDestinationType(new javax.imageio.ImageTypeSpecifier(
//                colorModel, colorModel.createCompatibleSampleModel(16, 16)));
                imgWriteParams.setDestinationType(new javax.imageio.ImageTypeSpecifier(
                        colorModel, colorModel.createCompatibleSampleModel(16, 16)));

                InputStream in = new FileInputStream(f);
                BufferedImage image = ImageIO.read(f);
                Integer heigth = image.getHeight();
                Integer width = image.getWidth();
                src = ImageIO.read(f);
                out = new FileOutputStream(imageCompress.getAbsolutePath()+"\\"+f.getName());
                log.debug("图片转换前大小"+f.length()+"字节");
                imgWrier.reset();
                // 必须先指定 out值，才能调用write方法, ImageOutputStream可以通过任何
                // OutputStream构造
                imgWrier.setOutput(ImageIO.createImageOutputStream(out));
                // 调用write方法，就可以向输入流写图片
                imgWrier.write(null, new IIOImage(src, null, null),
                        imgWriteParams);
                out.flush();
                out.close();
//                logger.debug("图片转换后大小"+file.length()+"字节");

                //写入exif信息
                copyExif(f, new File(imageCompress.getAbsolutePath()+"\\"+f.getName()), filePath);
            }else{
                log.error("对象是文件夹或文件不存在");
            }
        }
        deleteDir(imageCompress);
        return null;
    }

    /**
     *srcFile 原始照片  获取Exif信息
     *dstFile 压缩照片  不带Exif信息
     *filePath 最终照片存储路径
     */
    public static void copyExif(File srcFile, File dstFile, String filePath) throws Exception {
        File dst = new File(filePath+"\\result\\"+srcFile.getName());
        File dirs = new File(filePath+"\\result");
        dirs.mkdirs();
        FileOutputStream fos = new FileOutputStream(dst);
        OutputStream os = new BufferedOutputStream(fos);
        TiffOutputSet outputSet = null;

        final JpegImageMetadata dstMetadata = (JpegImageMetadata) Imaging.getMetadata(dstFile);
        final JpegImageMetadata srcMetadata = (JpegImageMetadata)Imaging.getMetadata(srcFile);
        // 图片可能没有元数据这里做一下判断
        if (null != dstMetadata) {
            TiffImageMetadata exif = dstMetadata.getExif();
            if (null != exif) {
                outputSet = exif.getOutputSet();
            }
        }

        // 如果文件没有元数据就创建一个空的
        if (null == outputSet) {
            outputSet = new TiffOutputSet();
        }

        List<TiffOutputDirectory> srcList = srcMetadata.getExif().getOutputSet().getDirectories();
        for(TiffOutputDirectory td : srcList){
            System.out.println(td.description());
            outputSet.addDirectory(td);
        }
        new ExifRewriter().updateExifMetadataLossless(dstFile, os, outputSet);
    }

    public static Boolean deleteDir(File file){
        file.deleteOnExit();
        return true;
    }

}