package com.allcore.listener;

import com.allcore.entity.MessageSend;
import com.allcore.entity.MessageStruct;
import com.allcore.service.IMessageSendService;
import com.allcore.websocket.WebSocket;
import lombok.extern.slf4j.Slf4j;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.jackson.JsonUtil;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.ObjectUtil;
import com.allcore.system.entity.Dept;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserSearchClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * RabbitMq封装使用：消费者业务代码示例
 * <AUTHOR>
 * @Date 2022/8/29 10:11
 */
@Slf4j
@Service
public class TestListener extends MqListener<MessageStruct> {

	@Autowired
	private WebSocket webSocket;

	@Autowired
	private IUserSearchClient userSearchClient;

	@Autowired
	private IMessageSendService messageSendService;

    @Override
    public boolean onReceive(MessageStruct messageStruct) {
        System.out.println("TestListener接收到消息的："+messageStruct);
		//消息入库同时通知给指定用户
		sendMessageAndWebsocketNotice(messageStruct);
        return true;
    }

    @Override
    public void onError(MessageStruct messageStruct, Exception e) {
        System.out.println("业务代码出现异常，可在此处进行处理！");

    }

	/**
	 * 发送消息并通知指定用户
	 * @param messageStruct
	 */
	private void sendMessageAndWebsocketNotice(MessageStruct messageStruct){
		log.info("队列2，手动ACK，消息发送入库：{}", JsonUtil.toJson(messageStruct));

		//是否群发
		Dept dept = messageStruct.getDept();
		User receiver = messageStruct.getReceiver();
		//群发
		if(ObjectUtil.isNotEmpty(dept)){
			//查询改部门下的所有用户
			R<List<User>> listR = userSearchClient.listByDept(dept.getId());
			if(listR.isSuccess()==true && listR.getData()!=null){
				List<MessageSend> list = new ArrayList<>();
				List<String> idList = new ArrayList<>();
				listR.getData().forEach(user->{
					list.add(getPackageEntity(user));
					idList.add(user.getId());
				});
				messageSendService.saveBatch(list);

				//websocket在线通知
				webSocket.sendToMore(idList,"发送给订阅的用户数据123");//暂时先用字符串替代
			}
		}else if(ObjectUtil.isNotEmpty(receiver)){
			//单人发送
			MessageSend packageEntity = getPackageEntity(receiver);
			log.info("队列2，手动ACK，发送入库数据：{}", JsonUtil.toJson(packageEntity));
			messageSendService.save(packageEntity);

			//websocket在线通知
			webSocket.sendToOne(receiver.getId(),"发送给订阅的用户数据123", true);//暂时先用字符串替代

		}

	}

	/**
	 * 封装messageSend对象
	 * @param receiver 接收用户
	 * @return
	 */
	private MessageSend getPackageEntity(User receiver){
		log.info("队列2，手动ACK，接收消息用户信息：{}", JsonUtil.toJson(receiver));
		MessageSend entity = BeanUtil.newInstance(MessageSend.class);
		entity.setUserId(receiver.getId());
		entity.setMessageTypeId("1556857795515572226");
		entity.setMessageId("daec0f70e414f2a4ec08904e480f19da");
		entity.setCreateUser(AuthUtil.getUserId());
		entity.setUpdateUser(AuthUtil.getUserId());
		return entity;
	}
}
