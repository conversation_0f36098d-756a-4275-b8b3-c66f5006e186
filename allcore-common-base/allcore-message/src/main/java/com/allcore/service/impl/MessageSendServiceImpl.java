package com.allcore.service.impl;

import com.allcore.entity.MessageSend;
import com.allcore.mapper.MessageSendMapper;
import com.allcore.service.IMessageSendService;
import com.allcore.vo.MessageSendVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息发送表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Service
public class MessageSendServiceImpl extends ServiceImpl<MessageSendMapper, MessageSend> implements IMessageSendService {

	@Autowired
	private MessageSendMapper messageSendMapper;

	@Override
	public IPage<MessageSendVO> selectMessageSendPage(IPage<MessageSendVO> page, MessageSendVO messageSend) {
		return page.setRecords(baseMapper.selectMessageSendPage(page, messageSend));
	}

    @Override
    public List<MessageSendVO> listByUserId(String userId) {
		return messageSendMapper.listByUserId(userId);
    }

}
