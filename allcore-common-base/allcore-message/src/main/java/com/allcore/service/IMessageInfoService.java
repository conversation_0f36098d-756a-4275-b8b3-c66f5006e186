package com.allcore.service;

import com.allcore.entity.MessageInfo;
import com.allcore.vo.MessageInfoVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 消息表 服务类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
public interface IMessageInfoService extends IService<MessageInfo> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param messageInfo
	 * @return
	 */
	IPage<MessageInfoVO> selectMessageInfoPage(IPage<MessageInfoVO> page, MessageInfoVO messageInfo);

}
