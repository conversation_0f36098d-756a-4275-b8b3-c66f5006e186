package com.allcore.external.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.CollectionUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.external.constant.ExternalConstant;
import com.allcore.external.dto.PositionDeviceRequest;
import com.allcore.external.dto.PositionDeviceResponse;
import com.allcore.external.dto.RasDataByObjDTO;
import com.allcore.external.dto.RasDataBySKeyDTO;
import com.allcore.external.entity.PositionDevice;
import com.allcore.external.feign.IRasDataClient;
import com.allcore.external.mapper.PositionDeviceMapper;
import com.allcore.external.service.IPositionDeviceService;
import com.allcore.external.service.IRasDataService;
import com.allcore.external.utils.ras.OpenAPI;
import com.allcore.external.utils.ras.OpenAPIUtil;
import com.allcore.external.vo.RasDataVO;
import com.allcore.external.vo.rasStatistic.DataSeries;
import com.allcore.external.vo.rasStatistic.DualAxisChart;
import com.allcore.external.vo.rasStatistic.YAxisConfig;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.util.concurrent.AtomicDouble;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Var;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.*;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.allcore.common.constant.CommonConstant.*;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 14 7月 2025
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RasDataServiceImpl implements IRasDataService {

    private final IRasDataClient rasDataClient;
    private final IPositionDeviceService positionDeviceService;
    private final AllcoreRedis allcoreRedis;

    private static final DateTimeFormatter FMT=
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final ZoneId ZONE=ZoneId.of("Asia/Shanghai");

    private static final String DAY_POWER = "day_power";
    private static final String MONTH_POWER = "month_power";
    private static final String YEAR_POWER = "year_power";
    private static final String GENERATED_OUTPUT = "generated_output";
    private static final String FAN_MONTH_POWER = "fan:month_power:";


    @Override
    public Object dataByObj(RasDataByObjDTO dto) {
        RasDataVO result = rasDataClient.dataByObj(dto);
        if (!result.getSuccessful()) {
            throw new ServiceException(result.getResultHint());
        }
        return result.getResultData();
    }

    @Override
    public Map<String, Object> dataBySkey(RasDataBySKeyDTO dto) {
        RasDataVO result = rasDataClient.dataBySkey(dto);
        if (!result.getSuccessful()) {
            throw new ServiceException(result.getResultHint());
        }
        String str = JSON.toJSONString(result.getResultData());
        return JSON.parseObject(str, new TypeReference<Map<String, Object>>() {
        });
    }

    /**
     * 定时更新定位设备台账
     */
    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional(rollbackFor = Exception.class)
    public void getPositionDevice() {
        // 定时请求更新台账
        RasDataByObjDTO dto = new RasDataByObjDTO();
        dto.setObjId("1100");
        RasDataVO result = rasDataClient.dataByObj(dto);
        if (!result.getSuccessful() || result.getResultHint() == null) {
            return;
        }
        List<PositionDeviceResponse> positionDeviceDTOS = JSONUtil.toList(JSONUtil.toJsonStr(result.getResultData()), PositionDeviceResponse.class);
        if (CollectionUtil.isNotEmpty(positionDeviceDTOS)) {
            // 若有删除的设备先同步删除
            List<String> rasIds = positionDeviceDTOS.stream().map(PositionDeviceResponse::getId).collect(Collectors.toList());
            List<String> ids = positionDeviceService.list(new LambdaQueryWrapper<PositionDevice>()
                    .select(PositionDevice::getId)).stream().map(PositionDevice::getId).collect(Collectors.toList());
            List<String> removeIds = ids.stream().filter(id -> !rasIds.contains(id)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(removeIds)) {
                positionDeviceService.removeBatchByIds(removeIds);
                // 删除缓存 [position:device:-skey-deviceInfo]
                allcoreRedis.hDel(ExternalConstant.POSITION_DEVICE);
            }
            // 属性拷贝
            List<PositionDevice> positionDevices = BeanUtil.copy(positionDeviceDTOS, PositionDevice.class);
            // 单位填充
            if (CollectionUtil.isNotEmpty(positionDevices)) {
                positionDevices.forEach(e -> {
                    Dept dept = SysCache.getDept(e.getStid());
                    if (dept != null) {
                        e.setDeptCode(dept.getDeptCode());
                    }
                });
                // 台账入库
                positionDeviceService.saveOrUpdateBatch(positionDevices);
            }
        }
    }

    /**
     * 发电量完成率统计
     *
     * @return
     */
    @Override
    public DualAxisChart getPowerCompletionRate() {
        // 1. 设置x坐标
        DualAxisChart dualAxisChart = new DualAxisChart();
        List<String> months = getMonthsBetween(null, null);
        dualAxisChart.setXAxisData(months); // x坐标 过去半年的 年-月
        // 2. 设置y坐标
        YAxisConfig leftYAxisConfig = new YAxisConfig();
        leftYAxisConfig.setAxisLabel("发电量");
        leftYAxisConfig.setUnit("kWh"); // y坐标 发电量
        dualAxisChart.setYAxisData(CollectionUtil.ofImmutableList(leftYAxisConfig));

        // 3. 构造请求参数，发送请求
        RasDataByObjDTO dto = new RasDataByObjDTO();
        dto.setObjId("7032");
        dto.setAttrs(CollectionUtil.ofImmutableList("data_generate_month"));
        LocalDateTime endTime = getLastDayOfMonth();
        LocalDateTime startTime = getFirstDayOfStartMonth(endTime);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setCycleType("M"); // 以月为统计周期
        RasDataVO result = rasDataClient.dataByObj(dto);
        if (!result.getSuccessful()) {
            throw new ServiceException(result.getResultHint());
        }
        Map<String, Double> dataSeries = new TreeMap<>();
        JSONArray data = JSONUtil.parseArray(result.getResultData());
        // a. 遍历每个区设备 （包铝：东站 61个区，671个光伏逆变器）
        for (int i = 0; i < data.size(); i++) {
            JSONObject dataGenerateMonth = data.getJSONObject(i);
            JSONArray monthArray = dataGenerateMonth.getJSONArray("data_generate_month");
            // b. 遍历每个设备的每个月的发电量
            for (int j = 0; j < monthArray.size(); j++) {
                JSONObject monthObj = monthArray.getJSONObject(j);
                // c. 获取时间和月发电量
                String time = monthObj.getStr("time");
                String value = monthObj.getStr("value");
                // d. 处理时间和月发电量
                LocalDateTime localDateTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String timeKey = YearMonth.from(localDateTime).toString();
                double dataValue = value != null ? Double.parseDouble(value) : 0.0;
                dataSeries.put(timeKey, dataSeries.getOrDefault(timeKey, 0.0) + dataValue);
            }
        }

        // 4. 设置数据点
        DataSeries complatedDataSeries = new DataSeries();
        complatedDataSeries.setSeriesName("实际发电量");
        complatedDataSeries.setYaxisLabel("发电量");
        if (CollectionUtil.isNotEmpty(dataSeries)) {
            complatedDataSeries.setDataPoints(Arrays.asList(dataSeries.values().toArray()));
        } else {
            complatedDataSeries.setDataPoints(Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0));
        }
        dualAxisChart.setDataSeriesList(CollectionUtil.ofImmutableList(complatedDataSeries));

        return dualAxisChart;
    }

    /**
     * 效率及可利用率统计
     *
     * @return
     */
    @Override
    public DualAxisChart getEfficiencyAndAvailability() {
        // 1. 设置x坐标
        DualAxisChart dualAxisChart = new DualAxisChart();
        List<String> months = getMonthsBetween(null, null);
        dualAxisChart.setXAxisData(months); // x坐标 过去半年的 年-月
        // 2. 设置y坐标
        YAxisConfig leftYAxisConfig = new YAxisConfig();
        leftYAxisConfig.setAxisLabel("百分比");
        leftYAxisConfig.setUnit("%"); // y坐标 百分比
        dualAxisChart.setYAxisData(CollectionUtil.ofImmutableList(leftYAxisConfig));
        // 3. 构造请求参数，发送请求
        RasDataByObjDTO dto = new RasDataByObjDTO();
        dto.setObjId("7032");
        dto.setAttrs(CollectionUtil.ofImmutableList("inv_eff"));
        LocalDateTime endTime = getLastDayOfMonth();
        LocalDateTime startTime = getFirstDayOfStartMonth(endTime);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setCycleType("M"); // 以月为统计周期
        RasDataVO result = rasDataClient.dataByObj(dto);
        if (!result.getSuccessful()) {
            throw new ServiceException(result.getResultHint());
        }
        Map<String, Double> dataSeries = new TreeMap<>();
        JSONArray data = JSONUtil.parseArray(result.getResultData());
        // a. 遍历每个区逆变器 （包铝：东站 61个区，671个光伏逆变器）
        int numberOfInverters = data.size();
        for (int i = 0; i < numberOfInverters; i++) {
            JSONObject dataGenerateMonth = data.getJSONObject(i);
            JSONArray monthArray = dataGenerateMonth.getJSONArray("inv_eff");
            // b. 遍历每个逆变器的每个月的效率
            for (int j = 0; j < monthArray.size(); j++) {
                JSONObject monthObj = monthArray.getJSONObject(j);
                // c. 获取时间和效率
                String time = monthObj.getStr("time");
                String value = monthObj.getStr("value");
                // d. 处理时间和效率
                LocalDateTime localDateTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                String timeKey = YearMonth.from(localDateTime).toString();
                double dataValue = value != null ? Double.parseDouble(value) : 0.0;
                dataSeries.put(timeKey, dataSeries.getOrDefault(timeKey, 0.0) + dataValue);
            }
        }
        // 4. 设置数据点
        DataSeries complatedDataSeries = new DataSeries();
        complatedDataSeries.setSeriesName("逆变器平均效率");
        complatedDataSeries.setYaxisLabel("百分比");
        if (CollectionUtil.isNotEmpty(dataSeries)) {
            // 累加的效率/逆变器数量
            DecimalFormat df = new DecimalFormat("0.00");
            dataSeries.entrySet().stream().forEach(e -> {
                dataSeries.put(e.getKey(), Double.parseDouble(df.format(e.getValue() / numberOfInverters)));
            });
            complatedDataSeries.setDataPoints(Arrays.asList(dataSeries.values().toArray()));
        } else {
            complatedDataSeries.setDataPoints(Arrays.asList(0.0, 0.0, 0.0, 0.0, 0.0, 0.0));
        }
        dualAxisChart.setDataSeriesList(CollectionUtil.ofImmutableList(complatedDataSeries));

        return dualAxisChart;
    }

    /**
     * 逆变器平均停机时间及平均中断时间
     *
     * @return
     */
    @Override
    public DualAxisChart getDownAndInterruptionTime() {
        DualAxisChart dualAxisChart = new DualAxisChart();
        List<String> months = getMonthsBetween(null, null);
        dualAxisChart.setXAxisData(months);// x坐标 过去半年的 年-月



        return null;
    }

    @Override
    public List<String> getAllInvSkey() {

        RasDataByObjDTO dto = new RasDataByObjDTO();
        dto.setObjId("7032");
        dto.setAttrs(Arrays.asList("name","skey"));
        RasDataVO result = rasDataClient.dataByObj(dto);
        if (!result.getSuccessful() ) {
            return Collections.emptyList();
        }
        List<PositionDeviceResponse> positionDeviceDTOS = JSONUtil.toList(JSONUtil.toJsonStr(result.getResultData()), PositionDeviceResponse.class);
        if (CollectionUtil.isNotEmpty(positionDeviceDTOS)) {
            List<String>skeys=new ArrayList<>();
            positionDeviceDTOS.forEach(e->{
                if(e.getName().contains("逆变器")){
                    skeys.add(e.getSkey());
                }

            });
            log.info("总共{}个逆变器",skeys.size());
            return skeys;
        }
        return Collections.emptyList();
    }

    @Override
    public Map<String, String> getAvgInvTime() {

        // 获取所有逆变器skey
        List<String> allInvSkey = this.getAllInvSkey();
        YearMonth now =YearMonth.now(ZONE);

        // 生成 6个月的YearMonth
        List<YearMonth> months= IntStream.rangeClosed(0,5)
                .mapToObj(now::minusMonths)
                .sorted()
                .collect(Collectors.toList());

        // 逐月调接口 + 计算
        Map<YearMonth,Double> result= new LinkedHashMap<>();
        for (YearMonth ym : months) {
            LocalDateTime start =ym.atDay(1).atStartOfDay();
            LocalDateTime end = ym.atEndOfMonth().atTime(LocalTime.MAX);

            //构造请求体
            RasDataBySKeyDTO dto =new RasDataBySKeyDTO();
            List<String> requestSkeys =allInvSkey.stream()
                    .flatMap(id-> Stream.of(
                          "ras_pv:"+id+":inv_on_time",
                            "ras_pv:"+id+":inv_off_time"
                    ))
                    .collect(Collectors.toList());
            dto.setSkeys(requestSkeys);
            dto.setStartTime(start.format(FMT));
            dto.setEndTime(end.format(FMT));
            dto.setCycleType("D");
            dto.setGroupType("LAST");

            // feign调用
            RasDataVO rasDataVO = rasDataClient.dataBySkey(dto);
            if(!rasDataVO.getSuccessful()|| Func.isEmpty(rasDataVO.getResultData())){
                return Collections.emptyMap();
            }

            Map<String,List<Map<String,Object>>>data=
                    (Map<String, List<Map<String, Object>>>) rasDataVO.getResultData();
            double avgSec=avgOfMonth(data);

        }



        return Collections.emptyMap();
    }

    private double avgOfMonth(Map<String, List<Map<String, Object>>> data) {
        Map<String,Map<LocalDate,Long[]>> map=new HashMap<>();

        data.forEach((fullKey,list) ->{
            boolean on =fullKey.endsWith(":inv_on_time");
            boolean off=fullKey.endsWith(":inv_off_time");
            if(!on && !off) return;
            String inverterId = fullKey.substring("ras_pv:".length(),
                    fullKey.indexOf(':'));
            for (Map<String, Object> item : list) {
                // 跳过缺失 value
                if(!item.containsKey("value") || item.get("vavlue")==null){
                    continue;
                }
                LocalDate day = LocalDate.parse(item.get("time").toString(),FMT);
                long ts =((Number) item.get("value")).longValue();

                map.computeIfAbsent(inverterId,k->new HashMap<>())
                        .computeIfAbsent(day,d->new Long[2])[on ? 0:1]=ts;
            }
        });

        // 汇总
        int totalSec=0;
        int count=0;
        for( Map<LocalDate,Long[]> days: map.values()){
            for (Long[] arr : days.values()) {

            }
        }



        return 0;
    }

    /**
     * 校验并获取中间月份（包含起始月 结束月）
     *
     * @param startDate
     * @param endDate
     * @return
     */
    private static List<String> getMonthsBetween(String startDate, String endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth start = null;
        YearMonth end = null;
        if (StringUtil.isBlank(startDate) || StringUtil.isBlank(endDate)) {
            LocalDate now = LocalDate.now();
            LocalDate date = now.minus(5, ChronoUnit.MONTHS);
            start = YearMonth.from(date);
            end = YearMonth.from(now);
        } else {
            start = YearMonth.parse(startDate, formatter);
            end = YearMonth.parse(endDate, formatter);
        }
        List<String> months = new ArrayList<>();
        for (YearMonth ym = start; !ym.isAfter(end); ym = ym.plusMonths(1)) {
            months.add(ym.toString());
        }
        return months;
    }

    private static LocalDateTime getLastDayOfMonth() {
        // 获取当前时间（
        LocalDateTime now = LocalDateTime.now();

        // 1. 计算当月最后一天（23:59:59）
        LocalDateTime lastDayOfMonth = now
                // 调整到当月最后一天
                .with(TemporalAdjusters.lastDayOfMonth())
                // 设置时分秒为23:59:59
                .withHour(23)
                .withMinute(59)
                .withSecond(59)
                .withNano(0); // 忽略纳秒
        return lastDayOfMonth;
    }

    private static LocalDateTime getFirstDayOfStartMonth(LocalDateTime lastDayOfMonth) {
        // 2. 计算前半年开始月的第一天（00:00:00）
        // 前半年 (包含当前月)= 当前月 - 5个月
        YearMonth currentYearMonth = YearMonth.from(lastDayOfMonth);
        YearMonth startMonthOfLastHalfYear = currentYearMonth.minusMonths(5);
        LocalDateTime firstDayOfStartMonth = startMonthOfLastHalfYear
                // 当月第一天
                .atDay(1)
                // 设置时分秒为00:00:00
                .atStartOfDay();
        return firstDayOfStartMonth;
    }

    @Override
    public Map<String, Object> getLargeScreenStatistics() {
        Map<String, Object> result = new TreeMap<>();
        Map<String, Double> pvStatistic = getPvStatistic();
        Map<String, Double> fanStatistic = getFanStatistic();

        result.put(DAY_POWER, (int) Math.round((pvStatistic.get(DAY_POWER) + fanStatistic.get(DAY_POWER)) / 1000.0));
        result.put(MONTH_POWER, (int) Math.round((pvStatistic.get(MONTH_POWER) + fanStatistic.get(MONTH_POWER)) / 1000.0));
        result.put(YEAR_POWER, (int) Math.round((pvStatistic.get(YEAR_POWER) + fanStatistic.get(YEAR_POWER)) / 1000.0));
        result.put(GENERATED_OUTPUT, pvStatistic.get(GENERATED_OUTPUT) + fanStatistic.get(GENERATED_OUTPUT));
        result.put("station_num",2);
        result.put("capacity",1200);

        return result;
    }

    private Map<String, Double> getPvStatistic() {
        // 构造请求参数，发送请求
        RasDataByObjDTO dto = new RasDataByObjDTO();
        dto.setObjId("7032");
        // 日发电量，月发电量，年发电量，有功功率
        dto.setAttrs(CollectionUtil.ofImmutableList("data_generate_day", "data_generate_month", "data_generate_year", "data_cell_p"));
        RasDataVO result = rasDataClient.dataByObj(dto);
        if (!result.getSuccessful()) {
            throw new ServiceException(result.getResultHint());
        }
        JSONArray data = JSONUtil.parseArray(result.getResultData());
        AtomicDouble daySum = new AtomicDouble();
        AtomicDouble monthSum = new AtomicDouble();
        AtomicDouble yearSum = new AtomicDouble();
        AtomicDouble cellSum = new AtomicDouble();
        // a. 遍历每个区设备 （包铝：东站 61个区，671个光伏逆变器）
        for (int i = 0; i < data.size(); i++) {
            JSONObject object = data.getJSONObject(i);
            String day = object.getStr("data_generate_day");
            String month = object.getStr("data_generate_month");
            String year = object.getStr("data_generate_year");
            String cellP = object.getStr("data_cell_p");
            if (StringUtil.isAllBlank(day, month, year, cellP)) {
                continue;
            }
            // b. 处理区数据
            double dayValue = StringUtil.isNotBlank(day) ? Double.parseDouble(day) : 0.0;
            double monthValue = StringUtil.isNotBlank(month) ? Double.parseDouble(month) : 0.0;
            double yearValue = StringUtil.isNotBlank(year) ? Double.parseDouble(year) : 0.0;
            double cellPValue = StringUtil.isNotBlank(cellP) ? Double.parseDouble(cellP) : 0.0;
            // c. 取数据累加
            daySum.addAndGet(dayValue);
            monthSum.addAndGet(monthValue);
            yearSum.addAndGet(yearValue);
            cellSum.addAndGet(cellPValue);
        }
        Map<String, Double> map = new HashMap<>();
        map.put(DAY_POWER, daySum.get());
        map.put(MONTH_POWER, monthSum.get());
        map.put(YEAR_POWER, yearSum.get());
        map.put(GENERATED_OUTPUT, cellSum.get());
        return map;
    }

    private Map<String, Double> getFanStatistic() {
        // 构造请求参数，发送请求
        RasDataByObjDTO dto = new RasDataByObjDTO();
        dto.setObjId("7037");
        // 日发电量，有功功率
        dto.setAttrs(CollectionUtil.ofImmutableList("Unit_daily_gen", "Active_power"));
        RasDataVO result = rasDataClient.dataByObj(dto);
        if (!result.getSuccessful()) {
            throw new ServiceException(result.getResultHint());
        }
        JSONArray data = JSONUtil.parseArray(result.getResultData());
        AtomicDouble daySum = new AtomicDouble();
        AtomicDouble activeSum = new AtomicDouble();
        // a. 遍历风机设备 （包铝：西站 99个，东站 41个 共140个）
        for (int i = 0; i < data.size(); i++) {
            JSONObject object = data.getJSONObject(i);
            String day = object.getStr("Unit_daily_gen");
            String activePower = object.getStr("Active_power");
            if (StringUtil.isAllBlank(day, activePower)) {
                continue;
            }
            // b. 处理区数据(日发电量、有功功率)
            double dayValue = StringUtil.isNotBlank(day) ? Double.parseDouble(day) : 0.0;
            double activePowerValue = StringUtil.isNotBlank(activePower) && Double.parseDouble(activePower) > 0 ? Double.parseDouble(activePower) : 0.0;

            daySum.addAndGet(dayValue);
            activeSum.addAndGet(activePowerValue);
        }
        // c. 单独处理月、年发电量
        LocalDate now = LocalDate.now();
        double monthValue = allcoreRedis.hGet(FAN_MONTH_POWER, YearMonth.from(now).toString());
        double monthSum = monthValue + daySum.get();// 当日 + 当月初➡前一天累加发电量
        List<Double> list = allcoreRedis.hmGet(FAN_MONTH_POWER, getYearMonthsToCurrent(now).toArray());
        double yearValue = list.stream().mapToDouble(num -> num != null ? num : 0.0).sum();
        double yearSum = yearValue + daySum.get();// 当日 + 当月初➡前一天累加发电量 + 1月➡当月前一月

        Map<String, Double> map = new HashMap<>();
        map.put(DAY_POWER, daySum.get());
        map.put(MONTH_POWER, monthSum);
        map.put(YEAR_POWER, yearSum);
        map.put(GENERATED_OUTPUT, activeSum.get());

        return map;
    }

    /**
     * 获取从当年1月到当前月份的所有年月列表
     * @return 年月字符串列表，格式为 "yyyy-MM"
     */
    public static List<String> getYearMonthsToCurrent(LocalDate currentDate) {
        List<String> result = new ArrayList<>();
        int currentYear = currentDate.getYear();
        int currentMonth = currentDate.getMonthValue();
        // 从1月到当前月，逐个生成年月字符串
        for (int month = 1; month <= currentMonth; month++) {
            YearMonth yearMonth = YearMonth.of(currentYear, month);
            result.add(yearMonth.toString());
        }
        return result;
    }

    // 每天0点去统计当月发电量（月初-当前的前一天）
    @Scheduled(fixedRate = 10000)
    public void calculateFanMonthAndYear() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        // 获取前一天的最早时刻（00:00:00）
        LocalDateTime startOfDay = yesterday.atStartOfDay();
        // 获取前一天的最晚时刻（23:59:59）
        LocalDateTime endOfDay = yesterday.atTime(LocalTime.of(23, 59, 59));
        // 1. 构造请求参数，发送请求
        RasDataByObjDTO dto = new RasDataByObjDTO();
        dto.setObjId("7037");
        dto.setAttrs(CollectionUtil.ofImmutableList("Unit_daily_gen"));
        dto.setStartTime(startOfDay);
        dto.setEndTime(endOfDay);
        dto.setCycleType("D");
        dto.setGroupType("LAST");
        RasDataVO result = rasDataClient.dataByObj(dto);
        if (!result.getSuccessful()) {
            throw new ServiceException(result.getResultHint());
        }
        JSONArray data = JSONUtil.parseArray(result.getResultData());
        // a. 遍历风机设备 140个
        AtomicDouble monthSum = new AtomicDouble();
        for (int i = 0; i < data.size(); i++) {
            JSONObject object = data.getJSONObject(i);
            JSONArray unitDailyGen = object.getJSONArray("Unit_daily_gen");
            // b. 遍历每个设备的日发电量
            for (int j = 0; j < unitDailyGen.size(); j++) {
                JSONObject dayObj = unitDailyGen.getJSONObject(j);
                String value = dayObj.getStr("value");
                double dataValue = value != null ? Double.parseDouble(value) : 0.0;
                monthSum.addAndGet(dataValue);
            }
        }
        // 2. 得到全部设备的 月初➡前一天累加得到的月发电量
        allcoreRedis.hSet(FAN_MONTH_POWER, YearMonth.from(yesterday).toString(), monthSum.get());
    }

    @Override
    public Map<String, Object> getPvStatisticsForApp() {
        Map<String, Object> map = new HashMap<>();
        // 构造请求参数，发送请求
        RasDataByObjDTO dto = new RasDataByObjDTO();
        dto.setObjId("7032");
        // 日发电量，累计发电量
        dto.setAttrs(CollectionUtil.ofImmutableList("data_generate_day", "data_generate_sum"));
        RasDataVO result = rasDataClient.dataByObj(dto);
        if (!result.getSuccessful()) {
            throw new ServiceException(result.getResultHint());
        }
        JSONArray data = JSONUtil.parseArray(result.getResultData());
        AtomicDouble daySum = new AtomicDouble();
        AtomicDouble totalSum = new AtomicDouble();
        // 遍历每个区设备 （包铝：东站 61个区，671个光伏逆变器）
        for (int i = 0; i < data.size(); i++){
            JSONObject object = data.getJSONObject(i);
            String day = object.getStr("data_generate_day");
            String sum = object.getStr("data_generate_sum");
            double dayValue = StringUtil.isNotBlank(day) ? Double.parseDouble(day) : 0.0;
            double sumValue = StringUtil.isNotBlank(sum) ? Double.parseDouble(sum) : 0.0;
            daySum.addAndGet(dayValue);
            totalSum.addAndGet(sumValue);
        }
        map.put(DAY_SUM, daySum.get());
        map.put(TOTAL_SUM, totalSum.get());
        map.put(CAPACITY,1200);
        return map;
    }
}
