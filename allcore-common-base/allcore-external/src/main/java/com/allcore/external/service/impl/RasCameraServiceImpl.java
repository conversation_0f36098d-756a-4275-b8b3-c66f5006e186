package com.allcore.external.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.external.config.MachineProperties;
import com.allcore.external.dto.*;
import com.allcore.external.entity.CameraCoordinate;
import com.allcore.external.mapper.RasCameraMapper;
import com.allcore.external.service.IRasCameraService;
import com.allcore.external.utils.ras.OpenAPI;
import com.allcore.external.utils.ras.OpenAPIUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.json.JsonObject;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class RasCameraServiceImpl implements IRasCameraService {

    private final MachineProperties machineProperties;
    private final RasCameraMapper rasCameraMapper;

    @Override
    public R cameraDevicePage(CameraDeviceListRequest request) {
        R response = OpenAPIUtil.request(machineProperties.getRasHttpHost(), OpenAPI.CAMERA_DEVICE_LIST, request);
        log.info("收到RAS摄像机列表:{}",response.getData());
        return response;
    }

    @Override
    public R cameraChannelPage(CameraChannelListRequest request) {
        R response = OpenAPIUtil.request(machineProperties.getRasHttpHost(), OpenAPI.CAMERA_CHANNEL_LIST, request);
        log.info("收到RAS摄像机通道:{}", response);
        if(StringUtil.isNoneBlank(request.getPage(),request.getPageSize())){
            // 分页查询
            return response;
        }else {
            // 列表查询
            JSONArray array = JSONUtil.parseArray(response.getData());
            for (int i = 0; i < array.size(); i++) {
                JSONObject channel = array.getJSONObject(i);
                if (!channel.containsKey("ip")) {
                    continue;
                }
                String ip = (String)channel.get("ip");
                CameraCoordinate cameraCoordinate = rasCameraMapper.selectOne(new LambdaQueryWrapper<CameraCoordinate>().eq(CameraCoordinate::getCameraIp, ip));
                if (cameraCoordinate != null) {
                    channel.set("longitude", cameraCoordinate.getLongitude());
                    channel.set("latitude", cameraCoordinate.getLatitude());
                    channel.set("height", cameraCoordinate.getHeight());
                }
            }
            return R.data(array);
        }
    }

    @Override
    public R cameraControl(CameraControlRequest request) {
        R response = OpenAPIUtil.request(machineProperties.getRasHttpHost(), OpenAPI.PTZ_CONTROL, request);
        log.info("收到RAS摄像机云台控制:{}",response);
        return response;
    }

    @Override
    public R getRecordFile(CameraRecordRequest request) {
        R response = OpenAPIUtil.request(machineProperties.getRasHttpHost(), OpenAPI.RECORD_FILE, request);
        log.info("收到RAS录像文件列表数据{}",response.getData());
        return response;
    }

    @Override
    public R startPlayBack(CameraRecordRequest request) {
        R response = OpenAPIUtil.request(machineProperties.getRasHttpHost(), OpenAPI.START_PLAY_BACK, request);
        log.info("收到RAS开始历史回放:{}",response);
        return response;
    }

    @Override
    public R pausePlayBack(CameraRecordRequest request) {
        R response = OpenAPIUtil.request(machineProperties.getRasHttpHost(), OpenAPI.PAUSE_PLAY_BACK, request);
        log.info("收到RAS回放暂停/恢复:{}",response);
        return response;
    }

    @Override
    public R setPlayBackSpeed(CameraRecordRequest request) {
        R response = OpenAPIUtil.request(machineProperties.getRasHttpHost(), OpenAPI.PLAY_BACK_SPEED, request);
        log.info("收到RAS设置回访速度:{}",response);
        return response;
    }

    @Override
    public boolean saveCoordinate(CameraCoordinateSaveDTO dto) {
        CameraCoordinate cameraCoordinate = new CameraCoordinate();
        cameraCoordinate.setLatitude(dto.getLatitude());
        cameraCoordinate.setLongitude(dto.getLongitude());
        cameraCoordinate.setCameraIp(dto.getIp());
        int insert = rasCameraMapper.insert(cameraCoordinate);
        return insert > 0;
    }
}
