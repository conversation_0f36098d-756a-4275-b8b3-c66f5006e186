/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.allcore.external.service.impl;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.external.entity.ReceivePicDetail;
import com.allcore.external.mapper.ReceivePicDetailMapper;
import com.allcore.external.service.IReceivePicDetailService;
import org.springframework.stereotype.Service;

/**
 * 机巢配置指令表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Service
public class ReceivePicDetailServiceImpl extends ZxhcServiceImpl<ReceivePicDetailMapper, ReceivePicDetail> implements IReceivePicDetailService {

}
