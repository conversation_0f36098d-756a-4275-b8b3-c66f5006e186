package com.allcore.external.service;

import com.allcore.external.entity.AccidentData;
import com.allcore.external.entity.WaveData;

import java.util.List;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 08 5月 2025
 */
public interface ITransmissionLineWarnService {

    void saveWaveData(List<WaveData> data);

    void saveAccidentData(List<AccidentData> data);

    List<WaveData> getWaveDataInfo(String mongoId);
}
