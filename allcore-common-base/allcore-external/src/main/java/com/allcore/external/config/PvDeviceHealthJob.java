package com.allcore.external.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.api.R;
import com.allcore.external.dto.RasDataByObjDTO;
import com.allcore.external.dto.RasDataFilterDTO;
import com.allcore.external.entity.PvDeviceCurveData;
import com.allcore.external.feign.IRasDataClient;
import com.allcore.external.vo.RasDataVO;
import com.allcore.main.code.common.dto.DeviceCommonDTO;
import com.allcore.main.code.common.feign.ICommonClient;
import com.allcore.main.code.common.vo.BasicCommonVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 25 7月 2025
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PvDeviceHealthJob {

    private final IRasDataClient rasDataClient;
    private final ICommonClient commonClient;
    private final MongoTemplate mongoTemplate;

    @Scheduled(cron = "0 0 * * * ?")
    private void DispersionRateCalculation() {
        R<List<BasicCommonVO>> pvR = commonClient.getCommonDeviceList(DeviceCommonDTO.builder().deviceType(BizDictEnum.DEVICE_TYPE_PV.getCode()).build());
        if (!pvR.isSuccess()) {
            throw new ServiceException("获取光伏区域失败: " + pvR.getMsg());
        }
        List<BasicCommonVO> pvList = pvR.getData();
        pvList.stream().filter(pv -> pv.getDeviceName().startsWith("XBX-")).forEach(pv -> {
            String rasPvAreaName = getRasPvAreaName(pv.getDeviceName());
            RasDataFilterDTO filter = RasDataFilterDTO.builder().attr("name").comparator("like")
                    .value(Stream.of(rasPvAreaName + "%").collect(toList())).build();
            List<String> list = new ArrayList<>();
            list.add("name");
            for (int i = 1; i <= 28; i++) {
                list.add("inv_i_pv" + i);
            }
            RasDataByObjDTO dto = RasDataByObjDTO.builder().objId("7032").attrs(list)
                    .filters(Stream.of(filter).collect(toList())).build();
            RasDataVO rasDataVO = rasDataClient.dataByObj(dto);
            if (!rasDataVO.getSuccessful()) {
                throw new ServiceException("查询逆变器遥测数据失败: " + rasDataVO.getResultHint());
            }
            JSONArray array = JSONObject.parseArray(JSONObject.toJSONString(rasDataVO.getResultData()));
            for (int i = 0; i < array.size(); i++) {
                JSONObject item = array.getJSONObject(i);
                if (item.getString("name").equals(rasPvAreaName)) {
                    continue;
                }
                BigDecimal count = new BigDecimal(0);
                for (int j = 1; j <= 28; j++) {
                    count = count.add(item.getBigDecimal("inv_i_pv" + j));
                }
                BigDecimal average = count.divide(new BigDecimal(28), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal square = new BigDecimal(0);
                for (int j = 1; j <= 28; j++) {
                    BigDecimal inv = item.getBigDecimal("inv_i_pv" + j);
                    BigDecimal sub = inv.subtract(average);
                    square = square.add(sub.multiply(sub));
                }
                BigDecimal squareAvg = square.divide(new BigDecimal(28), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal sqrt = this.sqrt(squareAvg, 2);
                log.info("平均差, {} : {} ", item.getString("name"), sqrt);
                BigDecimal dispersionRate = BigDecimal.ZERO;
                if (!BigDecimal.ZERO.equals(average) && !BigDecimal.ZERO.equals(sqrt)) {
                    dispersionRate = sqrt.divide(average, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
                }
                log.info("离散率,{} : {}", item.getString("name"), dispersionRate);
                PvDeviceCurveData curveData = PvDeviceCurveData.builder()
                        .inverterNumber(getInverterNumber(item.getString("name"), pv.getDeviceName()))
                        .pvAreaId(pv.getDeviceId()).discreteRate(dispersionRate.floatValue())
                        .createTime(LocalDateTime.now()).build();
                mongoTemplate.save(curveData);
            }
        });
    }


    /**
     * 计算 BigDecimal 的平方根
     *
     * @param number    要开方的数字
     * @param precision 结果精度（小数位数）
     * @return 平方根结果
     */
    private BigDecimal sqrt(BigDecimal number, int precision) {
        // 1. 校验输入
        if (number.compareTo(BigDecimal.ZERO) < 0) {
            throw new ArithmeticException("不能对负数开方: " + number);
        }
        if (number.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        // 2. 设置计算环境
        MathContext mc = new MathContext(precision + 10, RoundingMode.HALF_UP);
        BigDecimal two = BigDecimal.valueOf(2);

        // 3. 初始估值（取 number/2）
        BigDecimal x = number.divide(two, mc);
        BigDecimal lastX;

        // 4. 牛顿迭代法求解
        int maxIterations = 100;
        do {
            lastX = x;
            // 牛顿迭代公式: x = (x + number/x)/2
            x = lastX.add(number.divide(lastX, mc)).divide(two, mc);
        } while (maxIterations-- > 0 &&
                x.subtract(lastX).abs().compareTo(BigDecimal.ONE.movePointLeft(precision + 5)) > 0);

        // 5. 返回精确结果
        return x.setScale(precision, RoundingMode.HALF_UP);
    }


    private String getRasPvAreaName(String name) {
        String[] split = name.split("-");
        if (split.length == 1) {
            return split[0];
        }
        return "东站-" + Integer.parseInt(split[1]) + "#光伏区";
    }

    private String getInverterNumber(String name, String pvAreaName) {
        String[] split = pvAreaName.split("-");
        Pattern pattern = Pattern.compile("光伏区(\\d+)#逆变器");
        Matcher matcher = pattern.matcher(name);
        if (!matcher.find()) {
            return name;
        }
        Integer num = Integer.parseInt(matcher.group(1));
        return "XBX-" + split[1] + "-" + String.format("%03d", num);
    }
}
