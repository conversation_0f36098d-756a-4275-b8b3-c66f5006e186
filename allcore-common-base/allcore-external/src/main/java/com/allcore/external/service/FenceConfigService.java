package com.allcore.external.service;

import com.allcore.core.mp.support.Query;
import com.allcore.external.dto.tetra.TetraPageDTO;
import com.allcore.external.entity.FenceConfig;
import com.allcore.external.entity.OribitialRobotConfig;
import com.allcore.external.vo.tetra.RobotInfoVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 09 5月 2025
 */
public interface FenceConfigService extends IService<FenceConfig> {


}
