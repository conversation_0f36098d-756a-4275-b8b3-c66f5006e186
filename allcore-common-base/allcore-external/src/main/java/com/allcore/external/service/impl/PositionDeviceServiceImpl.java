package com.allcore.external.service.impl;


import cn.hutool.json.JSONUtil;
import com.allcore.common.constant.Constant;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.CollectionUtil;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.external.config.MachineProperties;
import com.allcore.external.constant.ExternalConstant;
import com.allcore.external.dto.*;
import com.allcore.external.entity.PositionDevice;
import com.allcore.external.mapper.PositionDeviceMapper;
import com.allcore.external.service.IPositionDeviceService;
import com.allcore.external.utils.ras.OpenAPI;
import com.allcore.external.utils.ras.OpenAPIUtil;
import com.allcore.external.vo.PositionDeviceVO;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class PositionDeviceServiceImpl extends ServiceImpl<PositionDeviceMapper, PositionDevice> implements IPositionDeviceService {
    private final PositionDeviceMapper positionDeviceMapper;
    private final IUserClient userClient;
    private final MachineProperties machineProperties;
    private final AllcoreRedis allcoreRedis;

    @Override
    public IPage<PositionDeviceVO> selectPositionDevicePage(Query query, PositionDeviceQueryDTO dto) {
        IPage<PositionDevice> page = page(Condition.getPage(query), new LambdaQueryWrapper<PositionDevice>()
                .likeRight(dto.getDeptCode() != null, PositionDevice::getDeptCode, dto.getDeptCode())
                .eq(StringUtil.isNotBlank(dto.getDeviceId()), PositionDevice::getId, dto.getDeviceId())
                .eq(StringUtil.isNotBlank(dto.getComm()), PositionDevice::getComm, dto.getComm()));

        return page.convert(positionDevice -> {
            PositionDeviceVO vo = new PositionDeviceVO();
            BeanUtil.copyProperties(positionDevice, vo);
            translate(vo);
            return vo;
        });
    }

    private void translate(PositionDeviceVO vo) {
        if (vo != null) {
            // 单位名称
            Dept dept = SysCache.getDeptByDeptCode(vo.getDeptCode());
            if (dept != null) {
                vo.setDeptName(dept.getDeptName());
            }
            // 状态
            vo.setCommZh(vo.getComm() == 0 ? "离线" : vo.getComm() == 1 ? "在线" : "无状态");
            // 绑定人员
            R<User> userR = userClient.userInfoById(vo.getPeopleId());
            if (userR.isSuccess() && userR.getData() != null) {
                vo.setPeopleZh(userR.getData().getRealName());
            } else {
                vo.setPeopleZh("暂无");
            }
        }
    }


    @Override
    public R<PositionDeviceVO> getPositionDeviceById(String id) {
        PositionDevice positionDevice = positionDeviceMapper.selectById(id);
        if (positionDevice == null) {
            return R.fail("查询结果为空");
        }
        PositionDeviceVO vo = BeanUtil.copy(positionDevice, PositionDeviceVO.class);
        translate(vo);
        return R.data(vo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R modifyPositionDevice(PositionDeviceDTO dto) {
        PositionDevice positionDevice = this.getById(dto.getId());
        if (positionDevice == null) {
            return R.fail("未查询到目标，无法操作！");
        }
        // 绑定/解绑人员（同时绑定单位）
        String deptCode = "";
        User user = UserCache.getUser(dto.getPeopleId());
        if (user != null) {
            Dept dept = SysCache.getDept(user.getDeptId());
            if (dept != null) {
                deptCode = dept.getDeptCode();
            }
        }
        boolean update = this.update(new LambdaUpdateWrapper<PositionDevice>()
                .eq(PositionDevice::getId, positionDevice.getId())
                .set(PositionDevice::getPeopleId, StringUtil.isNotBlank(dto.getPeopleId()) ? dto.getPeopleId() : "")
                .set(PositionDevice::getDeptCode, deptCode));
        // 删除缓存 [position:device:-skey-deviceInfo]
        allcoreRedis.hDel(ExternalConstant.POSITION_DEVICE, positionDevice.getSkey());
        return R.status(update);
    }

    /**
     * 定时更新定位设备台账
     */
//    @Scheduled(cron = "0 0 1 * * ?")
//    @Transactional(rollbackFor = Exception.class)
//    @Override
//    public void getPositionDevice() {
//        // 定时请求更新台账
//        PositionDeviceRequest request = new PositionDeviceRequest();
//        request.setObjId(1100);
//        R result = OpenAPIUtil.request(machineProperties.getRasHttpHost(), OpenAPI.POSITION_API, request);
//        if (!result.isSuccess() || result.getData() == null) {
//            log.info("定位设备请求不成功或数据为空！");
//            return;
//        }
//        List<PositionDeviceResponse> positionDeviceDTOS = JSONUtil.toList(JSONUtil.toJsonStr(result.getData()), PositionDeviceResponse.class);
//        if (CollectionUtil.isNotEmpty(positionDeviceDTOS)) {
//            // 若有删除的设备先同步删除
//            List<String> rasIds = positionDeviceDTOS.stream().map(PositionDeviceResponse::getId).collect(Collectors.toList());
//            List<String> ids = positionDeviceMapper.selectList(new LambdaQueryWrapper<PositionDevice>()
//                    .select(PositionDevice::getId)).stream().map(PositionDevice::getId).collect(Collectors.toList());
//            List<String> removeIds = ids.stream().filter(id -> !rasIds.contains(id)).collect(Collectors.toList());
//            if (CollectionUtil.isNotEmpty(removeIds)) {
//                positionDeviceMapper.deleteBatchIds(removeIds);
//                // 删除缓存 [position:device:-deviceId-deviceInfo]
//                allcoreRedis.hDel(ExternalConstant.POSITION_DEVICE, removeIds);
//            }
//            // 属性拷贝
//            List<PositionDevice> positionDevices = BeanUtil.copy(positionDeviceDTOS, PositionDevice.class);
//            // 单位填充
//            if (CollectionUtil.isNotEmpty(positionDevices)) {
//                positionDevices.forEach(e -> {
//                    Dept dept = SysCache.getDept(e.getStid());
//                    if (dept != null) {
//                        e.setDeptCode(dept.getDeptCode());
//                    }
//                });
//                // 台账入库
//                this.saveOrUpdateBatch(positionDevices);
//            }
//        }
//    }

    /**
     * 获取设备实时数据接口
     */
    @Override
    public boolean openOrClosePositionRealTime(boolean close) {
        // 设置请求参数
        List<String> skeysList = new ArrayList<>();
        skeysList.add(Constant.RAS_POSITION_DEVICE + StringPool.ASTERISK + Constant.POSITION_X);
        skeysList.add(Constant.RAS_POSITION_DEVICE + StringPool.ASTERISK + Constant.POSITION_Y);
        skeysList.add(Constant.RAS_POSITION_DEVICE + StringPool.ASTERISK + Constant.POSITION_Z);
        PositionRealTimeRequest request = new PositionRealTimeRequest();
        request.setSkeys(skeysList);
        request.setWsSerial(Constant.WS_SERIAL);
        request.setWsGroup(Constant.WS_GROUP);
        if(close){
            request.setWsType("4"); // 4-停止推送
        }else {
            //1-只推送变化数据，2-有变化，推送group下全量数据；3-不管变不变化，定期推送；
            request.setWsType(machineProperties.getRasPositionWsType());
        }
        R result = OpenAPIUtil.request(machineProperties.getRasHttpHost(), OpenAPI.POSITION_WS_API, request);
        if (result.isSuccess()) {
            log.info("获取设备实时数据,前置HTTP得到的结果为{}", JSONUtil.toJsonStr(result.getData()));
            // HTTP请求获取响应坐标暂不处理
            return true;
        }
        return false;
    }


}
