package com.allcore.external.enums;

/**
 * base64文件类型,前缀
 * <AUTHOR>
public enum FenceAlarmEnum {

	ALARM_CODE_1101("1101", "1","紧急","个人救护报警，紧急按钮"),
	ALARM_CODE_1102("1102", "1","紧急","报到失败"),
	ALARM_CODE_1103("1103", "1","紧急","紧急报警"),
	ALARM_CODE_1104("1104", "1","紧急","紧急报警"),
	ALARM_CODE_1110("1110", "1","火警","火警报警"),
	ALARM_CODE_1111("1111", "1","火警","烟感探头"),
	ALARM_CODE_1112("1112", "1","火警","燃烧"),
	ALARM_CODE_1113("1113", "1","火警","消防水流"),
	ALARM_CODE_1114("1114", "1","火警","热感探头"),
	ALARM_CODE_1115("1115", "1","火警","火警手动报警"),
	ALARM_CODE_1117("1117", "1","火警","火焰探头"),
	ALARM_CODE_1118("1118", "1","火警","接近警报"),
	ALARM_CODE_1119("1119", "1","火警","煤气泄漏"),
	ALARM_CODE_1120("1120", "1","劫盗","劫盗"),
	ALARM_CODE_1121("1121", "1","劫盗","挟持"),
	ALARM_CODE_1122("1122", "1","劫盗","无声劫盗"),
	ALARM_CODE_1123("1123", "1","劫盗","有声劫盗"),
	ALARM_CODE_1124("1124", "1","窃盗","异地劫持"),
	ALARM_CODE_1130("1130", "1","窃盗","窃盗"),
	ALARM_CODE_1131("1131", "1","窃盗","周界窃盗"),
	ALARM_CODE_1132("1132", "1","窃盗","内部窃盗"),
	ALARM_CODE_1133("1133", "1","窃盗","24小时窃盗"),
	ALARM_CODE_1134("1134", "1","窃盗","出/入窃盗"),
	ALARM_CODE_1135("1135", "1","窃盗","日/夜防区"),
	ALARM_CODE_1136("1136", "1","窃盗","室外窃盗"),
	ALARM_CODE_1137("1137", "1","窃盗","拆动报警"),
	ALARM_CODE_1161("1161", "1","周界窃盗","攀爬报警"),
	ALARM_CODE_1170("1170", "1","故障","用户离线"),
	ALARM_CODE_1180("1180", "1","故障","GPRS设备断线"),
	ALARM_CODE_1181("1181", "1","周界窃盗","张力围栏报警"),
	ALARM_CODE_1190("1190", "1","周界窃盗","脉冲围栏断路报警"),
	ALARM_CODE_1191("1191", "1","周界窃盗","脉冲围栏短路报警"),
	ALARM_CODE_1192("1192", "1","周界窃盗","振动光纤断光"),
	ALARM_CODE_1193("1193", "1","周界窃盗","振动光纤触网"),
	;

	FenceAlarmEnum(String code, String level, String type, String content) {
		this.code = code;
		this.level = level;
		this.type = type;
		this.content = content;
	}

	/**
	 * 告警code
	 */
	private String code;
	/**
	 * 告警级别
	 */
	private String level;
	/**
	 * 告警类型
	 */
	private String type;
	/**
	 * 告警内容
	 */
	private String content;

	public String getCode() {
		return code;
	}

	public String getLevel() {
		return level;
	}

	public String getType() {
		return type;
	}

	public String getContent() {
		return content;
	}
	public static String getContent(String code) {
		for (FenceAlarmEnum fenceAlarmEnum : FenceAlarmEnum.values()) {
			if (fenceAlarmEnum.getCode().equals(code)) {
				return fenceAlarmEnum.getContent();
			}
		}
		return null;
	}
	public static String getType(String code) {
		for (FenceAlarmEnum fenceAlarmEnum : FenceAlarmEnum.values()) {
			if (fenceAlarmEnum.getCode().equals(code)) {
				return fenceAlarmEnum.getType();
			}
		}
		return null;
	}
	public static String getLevel(String code) {
		for (FenceAlarmEnum fenceAlarmEnum : FenceAlarmEnum.values()) {
			if (fenceAlarmEnum.getCode().equals(code)) {
				return fenceAlarmEnum.getLevel();
			}
		}
		return null;
	}
}
