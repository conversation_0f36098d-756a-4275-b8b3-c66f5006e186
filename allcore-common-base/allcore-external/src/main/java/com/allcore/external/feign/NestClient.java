package com.allcore.external.feign;

import com.allcore.core.tenant.annotation.NonDS;
import com.allcore.core.tool.api.R;
import com.allcore.external.dto.NestTaskDTO;
import com.allcore.external.dto.ReceivePicNewDTO;
import com.allcore.external.service.*;
import com.allcore.external.entity.Nest;
import com.allcore.external.entity.NestTask;
import com.allcore.external.entity.NestTaskDetail;
import com.allcore.external.service.INestService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 工单feign接口
 *
 * <AUTHOR>
 * @Date 2022/09/07 14:21
 **/
@NonDS
@RestController
@AllArgsConstructor
public class NestClient implements INestClient {

    private final INestService nestService;

    private final IReceivePicService iReceivePicService;

    private final IWeatherWarnService weatherWarnService;

    private final IRasDataService rasDataService;

    @Override
    public R saveBatch(List<NestTaskDTO> nestTaskDTOList) {
        return R.data(nestService.saveTaskBatch(nestTaskDTOList));
    }


    @Override
    public R saveDetailBatch(List<NestTaskDetail> nestTaskDetailList) {
        return R.data(nestService.saveDetailBatch(nestTaskDetailList));
    }

    @Override
    public R<NestTask> getNestTask(String airportNestId) {
        return R.data(nestService.getNestTask(airportNestId));
    }

    @Override
    public R<NestTaskDetail> getNestTaskDetail(String airportNestId) {
        return R.data(nestService.getNestTaskDetail(airportNestId));
    }

    @Override
    public void sendTaskToMachine(List<NestTaskDTO> nestTaskDTOList) {
    }

    @Override
    public R saveReceivePic(ReceivePicNewDTO dto) {
        return R.status(iReceivePicService.saveReceivePic(dto));
    }

    @Override
    public R<Boolean> excuteMission(String inspectionTaskId) {
        return R.status(nestService.excuteMission(inspectionTaskId));
    }

    @Override
    public R checkNestWeatherWarning(String siteId) {
        return weatherWarnService.checkNestWeatherWarning(siteId);
    }

    @Override
    public R getPvStatistics() {
        return R.data(rasDataService.getPvStatisticsForApp());
    }
}
