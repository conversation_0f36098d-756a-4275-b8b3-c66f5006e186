package com.allcore.external.service;

import com.allcore.external.dto.RasDataByObjDTO;
import com.allcore.external.dto.RasDataBySKeyDTO;
import com.allcore.external.vo.rasStatistic.DualAxisChart;

import java.util.List;
import java.util.Map;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 14 7月 2025
 */
public interface IRasDataService {

    Object dataByObj(RasDataByObjDTO dto);

    Map<String, Object> dataBySkey(RasDataBySKeyDTO dto);

    DualAxisChart getPowerCompletionRate();

    DualAxisChart getEfficiencyAndAvailability();

    DualAxisChart getDownAndInterruptionTime();

    List<String> getAllInvSkey();

    Map<String, String> getAvgInvTime();


    Map<String,Object> getLargeScreenStatistics();

    Map<String,Object> getPvStatisticsForApp();
}
