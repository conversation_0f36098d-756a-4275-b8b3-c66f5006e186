package com.allcore.external.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.jackson.JsonUtil;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.external.config.MachineProperties;
import com.allcore.external.constant.ExternalConstant;
import com.allcore.external.feign.IAirportClient;
import com.allcore.external.service.MachineNestOperateService;
import com.allcore.external.dto.*;
import com.allcore.external.utils.FeignTokenRetryExecutor;
import com.allcore.external.utils.MachineNestClientUtil;
import com.allcore.external.utils.UrlToMultipartFileConverter;
import com.allcore.external.vo.VideoStreamNewVO;
import com.allcore.external.vo.VideoStreamVO;
import com.allcore.external.vo.WeatherResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@Service
@Slf4j
public class MachineNestOperateServiceImpl implements MachineNestOperateService {

    @Value("${machine.three.url}")
    private String threeUrl;

    @Value("${machine.sn}")
    private String machineSn;

    @Value("${machine.uav.sn}")
    private String machineUavSn;

    @Resource
    private MachineProperties machineProperties;

    @Resource
    private IAirportClient client;

    @Resource
    private FeignTokenRetryExecutor executor;

    @Override
    public R issueInstruction(MachineNestOperateDTO dto) {
        try {
//			if(dto.getConfigDTO().getMockFlag()){
//				log.info("==================mock========issueInstruction=============");
//				return R.data("指令下发成功");
//			}
            Map headersMap = new HashMap(2);
            headersMap.put("HC-JC-ACCESSKEYID", machineProperties.getAccessKeyId());

            headersMap.put("HC-JC-ACCESSKEYSECRET", machineProperties.getAccessKeySecret());

            Map paramMap = new HashMap(2);
            paramMap.put("machineNestCode", dto.getMachineNestCode());
            paramMap.put("instructKey", dto.getInstructKey());
//			paramMap.put("configDTO", dto.getConfigDTO());
            String url = threeUrl + "issueInstruction";

            String body = MachineNestClientUtil.doPost(url, headersMap, MediaType.APPLICATION_JSON, paramMap);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if ((null != jsonObject) && (StringUtils.equalsIgnoreCase(jsonObject.getString("status"), "200"))) {
                return R.data("指令下发成功");
            }
            log.error("【{}】机巢下发指令失败，失败原因：【{}】", dto.getMachineNestCode(), body);
            return R.fail(body);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【{}】机巢下发指令失败", dto.getMachineNestCode());
        }
        return R.fail("失败");
    }

    @Override
    public R<VideoStreamNewVO> fetchVideoStream(VideoStreamQueryNewDTO dto) {
        try {

//			log.info("==================mock========fetchVideoStream=============");
//			VideoStreamNewVO responseBody = new VideoStreamNewVO();
//			if(dto.getUavOrAirport().equals(ExternalConstant.AIRPORT)){
//				responseBody.setUrlid(1);
//				responseBody.setPlayUrl("http://10.10.1.116:8081/live/airport.flv");
//			}
//			if(dto.getUavOrAirport().equals(ExternalConstant.UAV) && dto.getUavVideoType().equals(ExternalConstant.T)){
//				responseBody.setUrlid(2);
//				responseBody.setPlayUrl("http://10.10.1.116:8081/live/uav-t.flv");
//			}
//			if(dto.getUavOrAirport().equals(ExternalConstant.UAV) && dto.getUavVideoType().equals(ExternalConstant.Z)){
//				responseBody.setUrlid(3);
//				responseBody.setPlayUrl("http://10.10.1.116:8081/live/uav-z.flv");
//			}
//			return R.data(responseBody);


//			if("111111".equals(dto.getDeviceCode())){
//				log.info("==================mock========fetchVideoStream=============111111");
//				VideoStreamNewVO responseBody = new VideoStreamNewVO();
//				responseBody.setUrlid(13214);
//				responseBody.setPlayUrl("http://allcoretz.top:18888/live/11.flv");
//				return R.data(responseBody);
//			}else if("sn001".equals(dto.getDeviceCode())){
//				log.info("==================mock========fetchVideoStream=============sn001");
//				VideoStreamNewVO responseBody = new VideoStreamNewVO();
//				responseBody.setUrlid(13214);
//				responseBody.setPlayUrl("http://allcoretz.top:18888/live/11.flv");
//				return R.data(responseBody);
//			}

            Map headersMap = new HashMap();
            headersMap.put("HC-JC-ACCESSKEYID", machineProperties.getAccessKeyId());
            headersMap.put("HC-JC-ACCESSKEYSECRET", machineProperties.getAccessKeySecret());

            MultiValueMap paramMap = new LinkedMultiValueMap();
            if ("uav".equals(dto.getUavOrAirport())) {
                paramMap.add("code", dto.getDeviceCode());
                paramMap.add("type", "2");
                Map paramMap1 = new HashMap();
                paramMap1.put("code", dto.getDeviceCode());
                paramMap1.put("type", "2");
                String videoType = "t".equals(dto.getUavVideoType()) ? "ir" : "zoom";
                log.info("------------------------" + videoType + "--------------------------------");
                paramMap1.put("videoType", videoType);
                MachineNestClientUtil.doPost(threeUrl + "liveLensChange", headersMap, MediaType.APPLICATION_JSON, paramMap1);
            } else {
                paramMap.add("code", dto.getDeviceCode());
                paramMap.add("type", "1");
            }
            String url = threeUrl + "getVideoStream";
            String body = MachineNestClientUtil.doGet(url, headersMap, paramMap);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if ((null != jsonObject) && (StringUtils.equalsIgnoreCase(jsonObject.getString("status"), "200"))) {
                VideoStreamNewVO responseBody = new VideoStreamNewVO();
                List<HashMap> data = JsonUtil.parseArray(jsonObject.get("data").toString(), HashMap.class);
                responseBody.setUrlid(Integer.parseInt(String.valueOf(data.get(0).get("id"))));
                responseBody.setPlayUrl(String.valueOf(data.get(0).get("video_stream")));
                return R.data(responseBody);
            }
            log.error("【{}】机巢/无人机获取机巢/无人机播放视频流失败，失败原因：【{}】", dto.getDeviceCode(), body);
            return R.status(true);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【{}】机巢/无人机获取机巢/无人机播放视频流失败", dto.getDeviceCode());
        }
        return R.status(true);
    }

    @Override
    public Boolean airportTaskIssued(AirportOperationDTO dto) {
        JSONObject result = executor.execute("airport", () -> {
            try {
                return client.importKmlThenCreateManualMission(dto.getSiteId(),
                        "临时任务-" + System.currentTimeMillis(),
                        UrlToMultipartFileConverter.downloadFileAsMultipartFile(dto.getFilePath())
                );
            } catch (IOException e) {
                throw new ServiceException("飞控平台创建任务失败");
            }
        });
        if (!StringUtils.equals("00000", result.getString("errorCode"))) {
            throw new ServiceException(result.getString("errorMessage"));
        }
        String missionId = result.getJSONObject("data").getString("missionId");
        Map<String, Object> map = new HashMap<>(3);
        map.put("id", missionId);
        map.put("ignoreWeatherFlag", StringPool.ONE);
        JSONObject object = executor.execute("airport", () -> client.excuteMission(map));
        if (!StringUtils.equals("00000", object.getString("errorCode"))) {
            throw new ServiceException(object.getString("errorMessage"));
        }
        return true;
    }

    @Override
    public boolean issueInstructionNew(AirportOperationDTO dto) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("siteId", dto.getSiteId());
        JSONObject result;
        switch (dto.getInstructKey()) {
            case "1":
                result = executor.execute("airport", () -> client.openHatch(map));
                break;
            case "2":
                result = executor.execute("airport", () -> client.closeHatch(map));
                break;
            case "3":
                result = executor.execute("airport", () -> client.returnBase(map));
                break;
            default:
                return false;

        }
        if (!StringUtils.equals(result.getString("errorCode"), "00000")) {
            throw new ServiceException(result.getString("errorMessage") + "。 " + result.getString("userTips"));
        }
        return true;
    }


    @Override
    public void changeLens(ChangeLensDTO dto) {
        JSONObject result = executor.execute("airport", () -> client.changeLens(dto));
        if (!StringUtils.equals(result.getString("errorCode"), "00000")) {
            throw new ServiceException(result.getString("errorMessage"));
        }
    }

    @Override
    public R<List<VideoStreamVO>> getVideoStream(VideoStreamQueryDTO dto) {
        try {
//			if(dto.getConfigDTO().getMockFlag()){
//				log.info("==================mock========issueInstruction=============");
//				List<VideoStreamVO> rst = new ArrayList<>();
//				VideoStreamVO map = new VideoStreamVO();
//				if(dto.getType().equals(StringPool.ONE)){
//					map.setTag("机巢内部");
//					map.setVideoStream("https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv");
//					rst.add(map);
//					map.setTag("机巢外部");
//					map.setVideoStream("https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv");
//					rst.add(map);
//				}else{
//					map.setTag("无人机");
//					map.setVideoStream("https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv");
//					rst.add(map);
//				}
//				return R.data(rst);
//			}

            Map headersMap = new HashMap(2);
            headersMap.put("HC-JC-ACCESSKEYID", machineProperties.getAccessKeyId());
            headersMap.put("HC-JC-ACCESSKEYSECRET", machineProperties.getAccessKeySecret());

            MultiValueMap paramMap = new LinkedMultiValueMap();
            paramMap.add("code", dto.getCode());
            paramMap.add("type", dto.getType());
            paramMap.add("configDTO.accessKeyId", machineProperties.getAccessKeyId());
            paramMap.add("configDTO.accessKeySecret", machineProperties.getAccessKeySecret());
            paramMap.add("configDTO.serviceAddress", dto.getConfigDTO().getServiceAddress());
            String url = threeUrl + "getVideoStream";
            String body = MachineNestClientUtil.doGet(url, headersMap, paramMap);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if ((null != jsonObject) && (StringUtils.equalsIgnoreCase(jsonObject.getString("status"), "200"))) {
                List list = JSONObject.parseArray(jsonObject.getString("data"), VideoStreamVO.class);
                for (int i = 0; i < list.size(); i++) {
                    VideoStreamVO vo = (VideoStreamVO) list.get(i);
                    vo.setId(Integer.valueOf(i + 1));
                }
                return R.data(list);
            }
            log.error("【{}】机巢/无人机获取机巢/无人机播放视频流失败，失败原因：【{}】", dto.getCode(), body);
            return R.fail(body);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【{}】机巢/无人机获取机巢/无人机播放视频流失败", dto.getCode());
        }
        return R.fail("失败");
    }

    @Override
    public R workOrderDeliver(WorkOrderDeliverDTO dto) {
        try {

//			if(dto.getConfigDTO().getMockFlag()){
//				log.info("==================mock========workOrderDeliver=============");
//				return R.data("指令下发工单任务成功");
//			}

            MachineNestBrandConfigDTO configDTO = dto.getConfigDTO();

            Map headersMap = new HashMap(2);
            headersMap.put("HC-JC-ACCESSKEYID", configDTO.getAccessKeyId());
            headersMap.put("HC-JC-ACCESSKEYSECRET", configDTO.getAccessKeySecret());

            Map paramMap = new HashMap(3);
            paramMap.put("machineNestCode", dto.getMachineNestCode());
            paramMap.put("taskId", dto.getTaskId());
            paramMap.put("flyFilePath", dto.getFlyFilePath());
            paramMap.put("configDTO", dto.getConfigDTO());
            paramMap.put("taskType", dto.getTaskType());
            paramMap.put("fileType", dto.getFileType());
            paramMap.put("actionTriggerType", dto.getActionTriggerType());
            paramMap.put("actionTriggerParam", dto.getActionTriggerParam());
            String url = threeUrl + "workOrderDeliver";

            String body = MachineNestClientUtil.doPost(url, headersMap, MediaType.APPLICATION_JSON, paramMap);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if ((null != jsonObject) && (StringUtils.equalsIgnoreCase(jsonObject.getString("status"), "200"))) {
                return R.data("指令下发工单任务成功");
            }
            log.error("【{}】机巢下发工单任务失败，失败原因：【{}】", dto.getMachineNestCode(), body);
            return R.fail(body);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【{}】机巢下发工单任务失败", dto.getMachineNestCode());
        }
        return R.fail("失败");
    }

    @Override
    public R<List<WeatherResultVO>> getSevenWeather(WeatherResultDTO dto) {
//		if(dto.getConfigDTO().getMockFlag()){
//			List<WeatherResultVO> rst = new ArrayList<>();
//			WeatherResultVO one = new WeatherResultVO();
//			one.setDateStr("yyyy-MM-dd");
//			one.setExternalTemperature("30°C");
//			one.setWindSpeed("1km/h");
//			one.setWeather("多云");
//			one.setHumidity("79%");
//			rst.add(one);
//			rst.add(one);
//			rst.add(one);
//			rst.add(one);
//			rst.add(one);
//			rst.add(one);
//			rst.add(one);
//			log.info("==================mock======getSevenWeather===============");
//			return R.data(rst);
//		}
        try {
//			MachineNestBrandConfigDTO configDTO = dto.getConfigDTO();

            Map headersMap = new HashMap(2);
            headersMap.put("HC-JC-ACCESSKEYID", machineProperties.getAccessKeyId());
            headersMap.put("HC-JC-ACCESSKEYSECRET", machineProperties.getAccessKeySecret());

            MultiValueMap paramMap = new LinkedMultiValueMap();
            paramMap.add("machineNestCode", dto.getMachineNestCode());
//			paramMap.add("configDTO.accessKeyId", machineProperties.getAccessKeyId());
//			paramMap.add("configDTO.accessKeySecret", machineProperties.getAccessKeySecret());
//			paramMap.add("configDTO.serviceAddress", dto.getConfigDTO().getServiceAddress());
            String url = threeUrl + "getSevenWeather";
            String body = MachineNestClientUtil.doGet(url, headersMap, paramMap);
            JSONObject jsonObject = JSONObject.parseObject(body);
            if ((null != jsonObject) && (StringUtils.equalsIgnoreCase(jsonObject.getString("status"), "200"))) {
                List list = JSONObject.parseArray(jsonObject.getString("data"), WeatherResultVO.class);
                return R.data(list);
            }
            log.error("【{}】机巢获取近7天的天气情况失败，失败原因：【{}】", dto.getMachineNestCode(), body);
            return R.fail(body);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【{}】机巢获取近7天的天气情况失败", dto.getMachineNestCode());
        }
        return R.fail("失败");
    }
}
