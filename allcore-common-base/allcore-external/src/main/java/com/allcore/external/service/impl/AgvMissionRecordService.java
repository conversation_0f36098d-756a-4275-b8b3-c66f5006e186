package com.allcore.external.service.impl;

import com.allcore.external.config.AgvMissionRecordEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: bl
 * @description:
 * @author: fanxiang
 * @create: 2025-06-08 13:16
 **/
@Service
@Slf4j
public class AgvMissionRecordService {

    @Autowired
    private AgvMissionRecordEndpoint agvMissionRecordEndpoint;

    /**
     * 查询AGV任务记录
     * 发送查询请求，响应会通过WebSocket推送到前端
     *
     * @param agvIp AGV的IP地址
     * @return 是否发送成功
     */
    public boolean queryMissionRecord(String agvIp) {
        if (agvIp == null || agvIp.trim().isEmpty()) {
            log.warn("AGV IP地址不能为空");
            return false;
        }

        boolean success = agvMissionRecordEndpoint.queryMissionRecord(agvIp);

        if (success) {
            log.info("AGV任务记录查询请求已发送，IP: {}, 响应将推送到前端 /topic/agv_mission", agvIp);
        } else {
            log.error("AGV任务记录查询请求发送失败，IP: {}", agvIp);
        }

        return success;
    }

    /**
     * 检查AGV WebSocket连接状态
     *
     * @return 连接状态信息
     */
    public String getConnectionStatus() {
        boolean connected = agvMissionRecordEndpoint.isConnected();
        return "AGV任务记录WebSocket连接状态: " + (connected ? "已连接" : "未连接");
    }
}
