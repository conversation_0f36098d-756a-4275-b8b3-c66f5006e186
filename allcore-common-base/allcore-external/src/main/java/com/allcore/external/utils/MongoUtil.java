//package com.allcore.external.utils;
//
//import com.alibaba.fastjson.JSONObject;
//import com.allcore.core.tool.utils.SpringUtil;
//import com.allcore.core.tool.utils.StringUtil;
//import com.mongodb.BasicDBObject;
//import com.mongodb.DBObject;
//import com.mongodb.client.MongoCollection;
//import com.mongodb.client.result.DeleteResult;
//import com.mongodb.client.result.UpdateResult;
//import org.apache.poi.ss.formula.functions.T;
//import org.bson.Document;
//import org.springframework.context.annotation.Lazy;
//import org.springframework.data.mongodb.core.CollectionOptions;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.query.*;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//import org.springframework.util.StringUtils;
//
//import java.util.Collection;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//
///**
// * （Mongo db工具类）
// * <AUTHOR>
// * @date 2023/07/19 11:23
// * @param
// * @return
// */
////@Lazy
//@Component
//public class MongoUtil {
//    /** 无条件查询 */
//    public static final Query EMPTY_QUERY = new BasicQuery("{}");
//
//    public static MongoTemplate template;
//
//    static {
//        MongoUtil.template = SpringUtil.getBean(MongoTemplate.class);
//    }
//
//    /**
//     * 方法描述: query-by-id
//     *
//     * @param id
//     * @Return {@link Query}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:20:20
//     */
//    private static Query idQuery(String id) {
//        Criteria criteria = Criteria.where("id").is(id);
//        return Query.query(criteria);
//    }
//
//    /**
//     * 方法描述: multi-key-eq-val-query
//     *
//     * @param data
//     * @Return {@link Query}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:20:45
//     */
//    public static Query eqQuery(Map<String, Object> data) {
//        if (CollectionUtils.isEmpty(data)) {
//            return EMPTY_QUERY;
//        }
//        Criteria criteria = new Criteria();
//        for (Map.Entry<String, Object> entry : data.entrySet()) {
//            criteria.and(entry.getKey()).is(entry.getValue());
//        }
//        return Query.query(criteria);
//    }
//
//    /**
//     * 方法描述: single-key-eq-val-query
//     *
//     * @param field
//     * @param val
//     * @Return {@link Query}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:21:13
//     */
//    public static Query eqQuery(String field, Object val) {
//        assert !StringUtil.isBlank(field);
//        Criteria criteria = Criteria.where(field).is(val);
//        return Query.query(criteria);
//    }
//
//	/**
//	 * 方法描述: single-key-in-list-query
//	 *
//	 * @param field
//	 * @param list
//	 * @Return {@link Query}
//	 * @throws
//	 * <AUTHOR>
//	 * @date 2022年03月08日 11:21:13
//	 */
//	public static Query inQuery(String field, List<String> list) {
//		assert !StringUtil.isBlank(field);
//		Criteria criteria = Criteria.where(field).in(list);
//		return Query.query(criteria);
//	}
//
//    /**
//     * 方法描述: collection-name exists
//     *
//     * @param collectionName
//     * @Return {@link boolean}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:22:00
//     */
//    public static boolean collectionExists(String collectionName) {
//        return template.collectionExists(collectionName);
//    }
//
//    /**
//     * 方法描述: clazz exists
//     *
//     * @param clazz
//     * @throws
//     * @Return {@link boolean}
//     * <AUTHOR>
//     * @date 2019年12月05日 16:29:34
//     */
//    public static boolean collectionExists(Class<?> clazz) {
//        return template.collectionExists(clazz);
//    }
//
//    /**
//     * 方法描述: find by id
//     *
//     * @param id
//     * @param clazz
//     * @Return {@link K}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:24:28
//     */
//    public static <K> K findById(String id, Class<K> clazz) {
//        return template.findOne(idQuery(id), clazz);
//    }
//
//    /**
//     * 方法描述: get mongo collection by tableName
//     *
//     * @param tableName
//     * @Return {@link MongoCollection< Document>}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:25:17
//     */
//    public static MongoCollection<Document> getCollection(String tableName) {
//        assert !StringUtil.isBlank(tableName);
//        return template.getCollection(tableName);
//    }
//
//    /**
//     * 方法描述: create mongo collection by tableName
//     *
//     * @param tableName
//     * @Return {@link MongoCollection< Document>}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:25:32
//     */
//    public static MongoCollection<Document> createCollection(String tableName) {
//        assert !StringUtil.isBlank(tableName);
//        if (collectionExists(tableName)) {
//            return getCollection(tableName);
//        }
//        return template.createCollection(tableName);
//    }
//
//    /**
//     * 方法描述:  create mongo collection by tableName and collection-options
//     *
//     * @param tableName
//     * @param options
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:27:20
//     */
//    public static void createCollection(String tableName, CollectionOptions options) {
//        assert !StringUtil.isBlank(tableName);
//        if (collectionExists(tableName)) {
//            return;
//        }
//        template.createCollection(tableName, options);
//    }
//
//    /**
//     * 方法描述: create mongo collection by clazz
//     *
//     * @param clazz
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:29:03
//     */
//    public static void createCollection(Class<?> clazz) {
//        assert clazz != null;
//        if (collectionExists(clazz)) {
//            return;
//        }
//        template.createCollection(clazz);
//    }
//
//    /**
//     * 方法描述: drop collection
//     *
//     * @param tableName
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:29:18
//     */
//    public static void dropCollection(String tableName) {
//        assert !StringUtil.isBlank(tableName);
//        template.dropCollection(tableName);
//    }
//
//    /**
//     * 方法描述: insert
//     *
//     * @param t
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:29:28
//     */
//    public static void insert(Object t) {
//        assert t != null;
//        template.insert(t);
//    }
//
//    /**
//     * 方法描述:  batch insert in specified  table
//     *
//     * @param coll
//     * @param tableName
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:29:41
//     */
//    public static <K> void insertBatch(Collection<K> coll, String tableName) {
//        assert !StringUtil.isBlank(tableName) && !CollectionUtils.isEmpty(coll);
//        template.insert(coll, tableName);
//    }
//
//    /**
//     * 方法描述: batch insert (support multi tables)
//     *
//     * @param coll
//     * @Return {@link boolean}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:30:34
//     */
//    public static <K> boolean insertAll(Collection<K> coll) {
//        assert !CollectionUtils.isEmpty(coll);
//        template.insertAll(coll);
//        return true;
//    }
//
//    /**
//     * 方法描述: insert or update
//     *
//     * @param t
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:31:03
//     */
//    public static <K> void save(K t) {
//        assert t != null;
//        template.save(t);
//    }
//
//    /**
//     * 方法描述: update first
//     *
//     * @param t
//     * @param collectionName
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:31:40
//     */
//    public static <K> void save(K t, String collectionName) {
//        assert t != null && !StringUtil.isBlank(collectionName);
//        template.save(t, collectionName);
//    }
//
//    /**
//    * 方法描述: update first
//    *
//    * @param query
//    * @param update
//    * @param tableName
//    * @Return {@link boolean}
//    * @throws
//    * <AUTHOR>
//    * @date 2022年03月08日 11:31:50
//    */
//    public static boolean updateFirst(Query query, Update update, String tableName) {
//        assert update != null && !StringUtil.isBlank(tableName);
//        UpdateResult wr = template.updateFirst(query, update, tableName);
//        return wr.getModifiedCount() == 1;
//    }
//
//    /**
//     * 方法描述: update by id
//     *
//     * @param id
//     * @param obj
//     * @param clazz
//     * @Return {@link boolean}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:32:03
//     */
//    public static <K> boolean updateById(String id, JSONObject obj, Class<?> clazz) {
//        DBObject update = new BasicDBObject();
//        obj.put("update_time", new Date());
//        update.put("$set", obj);
//        UpdateResult result = template.updateFirst(idQuery(id), new BasicUpdate(update.toString()), clazz);
//        return result.getModifiedCount() == 1;
//    }
//
//    /**
//     * 方法描述: auto-inc specified  filed by id
//     *
//     * @param id
//     * @param field
//     * @param type
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:32:22
//     */
//    public static <K> void updateIncById(String id, String field, Class<K> type) {
//        Query query = idQuery(id);
//        Update inc = new Update().inc(field, 1);
//        template.updateFirst(query, inc, type);
//    }
//
//    /**
//     * 方法描述:  auto-inc specified  field by id
//     *
//     * @param id
//     * @param field
//     * @param collectionName
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:32:47
//     */
//    public static void updateIncById(String id, String field, String collectionName) {
//        Query query = idQuery(id);
//        Update inc = new Update().inc(field, 1);
//        template.updateFirst(query, inc, collectionName);
//    }
//
//    /**
//     * 方法描述:  auto-inc specified  field specified step by id
//     *
//     * @param id
//     * @param field
//     * @param step
//     * @param type
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:33:10
//     */
//    public static <K> void updateIncById(String id, String field, Integer step, Class<K> type) {
//        if (step == 0) {
//            return;
//        }
//        Query query = idQuery(id);
//        Update inc = new Update().inc(field, step);
//        template.updateFirst(query, inc, type);
//    }
//
//    /**
//     * 方法描述:  auto-inc specified  field specified step by id
//     *
//     * @param id
//     * @param field
//     * @param num
//     * @param collectionName
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:33:10
//     */
//    public static void updateIncById(String id, String field, Integer num, String collectionName) {
//        if (num == 0) {
//            return;
//        }
//        Query query = idQuery(id);
//        Update inc = new Update().inc(field, num);
//        template.updateFirst(query, inc, collectionName);
//    }
//
//    /**
//     * 方法描述: multi update in specified table
//     *
//     * @param query
//     * @param update
//     * @param tableName
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:34:20
//     */
//    public static void updateMulti(Query query, Update update, String tableName) {
//        template.updateMulti(query, update, tableName);
//    }
//
//    /**
//     * 方法描述: 条件统计
//     *
//     * @param query
//     * @param collectionName
//     * @throws
//     * @Return {@link long}
//     * <AUTHOR>
//     * @date 2019年12月05日 16:16:19
//     */
//    public static long count(Query query, String collectionName) {
//        return template.count(query, collectionName);
//    }
//
//    /**
//     * 方法描述: search
//     *
//     * @param query
//     * @param clazz
//     * @Return {@link List<K>}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:34:52
//     */
//    public static <K> List<K> find(Query query, Class<K> clazz) {
//        return template.find(query, clazz);
//    }
//
//    /**
//     * 方法描述: delete by id
//     *
//     * @param id
//     * @param collectionName
//     * @Return {@link boolean}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:35:05
//     */
//    public static boolean deleteById(String id, String collectionName) {
//        DeleteResult result = template.remove(idQuery(id), collectionName);
//        return result.getDeletedCount() == 1;
//    }
//
//    /**
//     * 方法描述: delete by id
//     *
//     * @param id
//     * @param clazz
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:35:17
//     */
//    public static void deleteById(String id, Class<?> clazz) {
//        template.remove(idQuery(id), clazz);
//    }
//
//    /**
//     * 方法描述: batch delete
//     *
//     * @param query
//     * @param collectionName
//     * @Return
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:35:29
//     */
//    public static void deleteBatch(Query query, String collectionName) {
//        template.remove(query, collectionName);
//    }
//
//    /**
//     * 方法描述: count
//     *
//     * @param query
//     * @param clazz
//     * @Return {@link long}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:34:41
//     */
//    public static long count(Query query, Class<T> clazz) {
//        return template.count(query, clazz);
//    }
//
//    /**
//     * 方法描述: count by id ( determinte the record exists )
//     *
//     * @param id
//     * @param collectionName
//     * @Return {@link long}
//     * @throws
//     * <AUTHOR>
//     * @date 2022年03月08日 11:35:40
//     */
//    public static long countById(String id, String collectionName) {
//        if (StringUtil.isBlank(id) || StringUtil.isBlank(collectionName)) {
//            return 0L;
//        }
//        return template.count(idQuery(id), collectionName);
//    }
//
//    public static void resolve(T t) {
//
//    }
//
//}
