package com.allcore.user.mapper;


import com.allcore.user.entity.UserLoginLog;
import com.allcore.user.vo.UserLoginLogVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 */
public interface UserLoginLogMapper extends BaseMapper<UserLoginLog> {



	List<UserLoginLogVO> getUserWorkTime(@Param("fromType") String fromType, @Param("userId") String userId);
}
