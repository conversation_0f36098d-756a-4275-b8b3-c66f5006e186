package com.allcore.system.service.impl;


import com.allcore.core.mp.base.BaseServiceImpl;
import com.allcore.system.entity.DataScope;
import com.allcore.system.mapper.DataScopeMapper;
import com.allcore.system.service.IDataScopeService;
import org.springframework.stereotype.Service;

/**
 *  服务实现类
 *
 * <AUTHOR>
 */
@Service
public class DataScopeServiceImpl extends BaseServiceImpl<DataScopeMapper, DataScope> implements IDataScopeService {

}
