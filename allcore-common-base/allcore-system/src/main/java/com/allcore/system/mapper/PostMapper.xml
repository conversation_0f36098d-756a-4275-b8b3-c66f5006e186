<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.system.mapper.PostMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="postResultMap" type="com.allcore.system.entity.Post">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="category" property="category"/>
        <result column="post_code" property="postCode"/>
        <result column="post_name" property="postName"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
    </resultMap>


    <select id="selectPostPage" resultMap="postResultMap">
        select id,
               tenant_id,
               category,
               post_code,
               post_name,
               sort,
               remark,
               create_user,
               create_dept,
               create_time,
               update_user,
               update_time,
               status,
               is_deleted from sys_post where is_deleted = 0
    </select>

    <select id="getPostNames" resultType="java.lang.String">
        SELECT
            post_name
        FROM
            sys_post
        WHERE
            id IN
            <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
                #{ids}
            </foreach>
            and is_deleted = 0
    </select>

</mapper>
