package com.allcore.system.service.impl;


import com.allcore.core.mp.base.BaseServiceImpl;
import com.allcore.system.entity.TenantPackage;
import com.allcore.system.mapper.TenantPackageMapper;
import com.allcore.system.service.ITenantPackageService;
import org.springframework.stereotype.Service;

/**
 * 租户产品表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class TenantPackageServiceImpl extends BaseServiceImpl<TenantPackageMapper, TenantPackage> implements ITenantPackageService {

}
