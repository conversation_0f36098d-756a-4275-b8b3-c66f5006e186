package com.allcore.system.mapper;

import com.allcore.core.tool.node.TreeNode;
import com.allcore.system.dto.MenuDTO;
import com.allcore.system.entity.Menu;
import com.allcore.system.vo.MenuVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


/**
 * MenuMapper 接口
 *
 * <AUTHOR>
 */
public interface MenuMapper extends BaseMapper<Menu> {

	/**
	 * （懒加载列表 名称模糊+code模糊+别名模糊+父id）
	 * <AUTHOR>
	 * @date 2023/03/22 18:46
	 * @param parentId
	 * @param param
	 * @return java.util.List<com.allcore.system.vo.MenuVO>
	 */
	List<MenuVO> lazyList(String parentId, Map<String, Object> param);

	/**
	 * （懒加载列表:光菜单 名称模糊+code模糊+别名模糊+父id）
	 * <AUTHOR>
	 * @date 2023/03/22 18:51
	 * @param parentId
	 * @param param
	 * @return java.util.List<com.allcore.system.vo.MenuVO>
	 */
	List<MenuVO> lazyMenuList(String parentId, Map<String, Object> param);

	/**
	 * 树形结构
	 *
	 * @return
	 */
	List<TreeNode> tree();

	/**
	 * 授权树形结构
	 *
	 * @return
	 */
	List<TreeNode> grantTree();

	/**
	 * 授权树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<TreeNode> grantTreeByRole(@Param("roleId") List<String> roleId, @Param("refType") String refType);

	/**
	 * 顶部菜单树形结构
	 *
	 * @return
	 */
	List<TreeNode> grantTopTree();

	/**
	 * 顶部菜单树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<TreeNode> grantTopTreeByRole(@Param("roleId") List<String> roleId, @Param("refType") String refType);

	/**
	 * 数据权限授权树形结构
	 *
	 * @return
	 */
	List<TreeNode> grantDataScopeTree();

	/**
	 * 接口权限授权树形结构
	 *
	 * @return
	 */
	List<TreeNode> grantApiScopeTree();

	/**
	 * 数据权限授权树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<TreeNode> grantDataScopeTreeByRole(@Param("roleId") List<String> roleId, @Param("refType") String refType);

	/**
	 * 接口权限授权树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<TreeNode> grantApiScopeTreeByRole(@Param("roleId") List<String> roleId, @Param("refType") String refType);

	/**
	 * 所有菜单
	 *
	 * @return
	 */
	List<Menu> allMenu();

	/**
	 * 权限配置菜单
	 *
	 * @param roleId
	 * @param topMenuId
	 * @return
	 */
	List<Menu> roleMenu(@Param("roleId") List<String> roleId, @Param("topMenuId") String topMenuId, @Param("refType") String refType);

	/**
	 * 权限配置菜单
	 *
	 * @param roleId
	 * @return
	 */
	List<Menu> roleMenuByRoleId(@Param("roleId") List<String> roleId, @Param("refType") String refType);

	/**
	 * 权限配置菜单
	 *
	 * @param topMenuId
	 * @return
	 */
	List<Menu> roleMenuByTopMenuId(String topMenuId);

	/**
	 * 菜单树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<Menu> routes(@Param("roleId") List<String> roleId, @Param("refType") String refType);

	/**
	 * 按钮树形结构
	 *
	 * @return
	 */
	List<Menu> allButtons();

	/**
	 * 按钮树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<Menu> buttons(@Param("roleId") List<String> roleId, @Param("refType") String refType);

	/**
	 * 获取配置的角色权限
	 *
	 * @param roleIds
	 * @return
	 */
	List<MenuDTO> authRoutes(@Param("roleIds") List<String> roleIds, @Param("refType") String refType);

	/**
	 * （根据历史id查菜单）
	 * <AUTHOR>
	 * @date 2022/10/09 10:23
	 * @param oldId
	 * @return
	 */
    Menu getMenuByOldId(@Param("oldId") String oldId);
}
