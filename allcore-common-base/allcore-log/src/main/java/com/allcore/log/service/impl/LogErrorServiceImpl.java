package com.allcore.log.service.impl;

import com.allcore.core.log.model.LogError;
import com.allcore.log.mapper.LogErrorMapper;
import com.allcore.log.service.ILogErrorService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class LogErrorServiceImpl extends ServiceImpl<LogErrorMapper, LogError> implements ILogErrorService {

}
