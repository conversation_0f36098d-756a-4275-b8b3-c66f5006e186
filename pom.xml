<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.allcore</groupId>
    <artifactId>allcore-product-biz</artifactId>
    <version>PRODUCT.1.0.0.RELEASE</version>
    <packaging>pom</packaging>
    <description>众芯汉创公司级产品底座</description>

    <properties>
        <allcore.tool.version>1.0.0.RELEASE</allcore.tool.version>
        <allcore.project.version>PRODUCT.1.0.0.RELEASE</allcore.project.version>
        <java.version>1.8</java.version>
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <flowable.version>6.4.2</flowable.version>
        <dubbo.version>2.7.8</dubbo.version>

        <spring.boot.version>2.7.1</spring.boot.version>
        <spring.cloud.version>2021.0.3</spring.cloud.version>
        <spring.platform.version>Cairo-SR8</spring.platform.version>

        <!--harbor服务器-->
        <docker.registry.url>***********</docker.registry.url>
        <docker.registry.host>http://${docker.registry.url}:2375</docker.registry.host>
        <docker.username>admin</docker.username>
        <!--某个docker服务器 需要已经docker login zxhc.io-->
        <docker.host>http://***********:2375</docker.host>
        <docker.password>Zxhc@1234hb</docker.password>
        <docker.namespace>allcore-product</docker.namespace>
        <docker.version>PRODUCT.1.0.0.RELEASE</docker.version>
        <docker.plugin.version>1.4.13</docker.plugin.version>

        <javax.annotation-api.version>1.3.2</javax.annotation-api.version>
        <slf4j-api.version>1.7.36</slf4j-api.version>
        <netty-all.version>4.1.63.Final</netty-all.version>
        <mybatis-spring-boot-starter.version>2.2.2</mybatis-spring-boot-starter.version>
        <dameng.version>*********</dameng.version>
    </properties>

    <modules>
        <module>allcore-common</module>
        <module>allcore-common-base</module>
        <module>allcore-common-base-api</module>
        <module>allcore-business</module>
        <module>allcore-business-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.allcore.platform</groupId>
                <artifactId>allcore-bom</artifactId>
                <version>${allcore.tool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.spring.platform</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${spring.platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>Dm7JdbcDriver18</artifactId>
                <version>${dameng.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                        <!--设置为true，以便把本地的system的jar也包括进来-->
                        <includeSystemScope>true</includeSystemScope>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>${docker.plugin.version}</version>
                    <configuration>
                        <username>${docker.username}</username>
                        <password>${docker.password}</password>
                        <repository>${docker.registry.url}/${docker.namespace}/${project.artifactId}</repository>
                        <tag>${project.version}</tag>
                        <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                        <buildArgs>
                            <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                        </buildArgs>
                    </configuration>
                    <!--子服务添加如下配置，运行 mvn deploy 命令便会自动打包镜像-->
                    <!--<executions>
                        <execution>
                            <id>default</id>
                            <goals>
                                <goal>build</goal>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>-->
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>3.1.0</version>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <target>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          tofile="${session.executionRootDirectory}/target/${project.artifactId}.jar"
                                          file="${project.build.directory}/${project.artifactId}.jar"/>
                                </target>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>allcore-group</id>
            <url>http://***********:8081/repository/allcore-group/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>allcore-releases</id>
            <name>Release Repository</name>
            <url>http://***********:8081/repository/allcore-releases/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-plugin</id>
            <url>https://maven.aliyun.com/repository/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>allcore-releases</id>
            <name>Release Repository</name>
            <url>http://***********:8081/repository/allcore-releases/</url>
        </repository>
    </distributionManagement>

</project>
