package com.allcore.platform.vo.InnerOrdervo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class patrolRange {
    private String terrainType;
    private String ruleContext;
    private String patrolCycle;
    private String cycleUnit;
    private String editorId;

    private String editorName;

    private String mtime;
//    private String selectedScopeID;
//    private String whetherToPatrol;
//    private String extend;


//    // 馈线线路 ID
//    private String patrolFeeder;
//    // 设备名称
//    private String equipName;
//    // 设备坐标
//    private String psrCoordinate;
//    // 资源 id
//    private String psrId;
//    // 馈线名称
//    private String patrolFeederName;
//    // 是否显示(默认是)
//    private String isShow;
    // 扩展字段（json 格式）
    private String extend;
//    // 资产 id
//    private String astId;
    // 所选范围 ID
    private String selectedScopeID;
//    // 设备类型
//    private String equipType;
    // 是否完成巡视
    private String whetherToPatrol;
//    // 设备编号
//    private String equipNo;
//    // 设备真实类型
//    private String psrRealType;
//    // 是否必巡
//    private String isMustPatrol;

    private String towerId;
    private String towerNo;
    private String objId;
    private String groupId;

    private String ctime;
    private String dataSource;
    private String isDeleted;
    private String createrId;
    //private List<Map> towerRangeList;

}
