package com.allcore.platform.dto;


import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

@Data
public class WorkOrderZtDTO {

	/**
	 * 创建人
	 */
	private String createUser;
	private String userName;

	/**
	 * 工作许可人
	 */
	private String workLicensorGuid;

	/**
	 * 巡检工单GUID
	 */
	private String inspectGuid;

	/**
	 * 工单编号
	 */
	private String inspectNo;

	/**
	 * 中台objId
	 */
	private String ztObjId;

	/**
	 * 其他安全措施和注意事项
	 */
	private String otherSafeRemark;

	/**
	 * 巡检开始工作时间
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	private String inspectStartTime;

	/**
	 * 巡检结束工作时间
	 */
	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	private String inspectEndTime;

	/**
	 * 安全策略
	 */
	private String safeStrategy;

	/**
	 * 作业计划GUID
	 */
	private String planGuid;

	/**
	 * 工单状态Zh
	 */
	private String inspectStatusZh;

	/**
	 * 工单状态Zh
	 */
	private String inspectStatus;

	/**
	 * 无人机
	 */
	private String planeGuid;

	/**
	 * 终止原因
	 */
	private String inspectReserve4;

	/**
	 * 作业人员GUID
	 */
	private String operatorGuid;

	/**
	 * 人员名称
	 */
	private String operatorName;

	/**
	 * 单位GUID
	 */
	private String deptGuid;
	private String deptName;

	/**
	 * 所属地市
	 */
	private String cityCode;
	private String cityName;

	/**
	 * 第一个单位
	 */
	private String firstGuid;
	private String firstName;

	/**
	 * 第二个单位
	 */
	private String secondGuid;
	private String secondName;

	/**
	 * 第三个单位
	 */
	private String thirdGuid;
	private String thirdName;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 空域范围
	 */
	private String auditAirspace;


}
