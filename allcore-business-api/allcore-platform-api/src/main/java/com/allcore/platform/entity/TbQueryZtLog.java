package com.allcore.platform.entity;

import java.util.Date;

public class TbQueryZtLog {
    private Long id;

    private String entry;

    private String visit;

    private Date queryTime;

    private String interfaceName;

    private int module;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEntry() {
        return entry;
    }

    public void setEntry(String entry) {
        this.entry = entry == null ? null : entry.trim();
    }

    public String getVisit() {
        return visit;
    }

    public void setVisit(String visit) {
        this.visit = visit == null ? null : visit.trim();
    }

    public Date getQueryTime() {
        return queryTime;
    }

    public void setQueryTime(Date queryTime) {
        this.queryTime = queryTime;
    }

    public String getInterfaceName() {
        return interfaceName;
    }

    public void setInterfaceName(String interfaceName) {
        this.interfaceName = interfaceName == null ? null : interfaceName.trim();
    }

    public int getModule() {
        return module;
    }

    public void setModule(int module) {
        this.module = module;
    }

    public TbQueryZtLog() {
    }

    public TbQueryZtLog(String entry, String visit, String interfaceName, int module) {
        this.entry = entry;
        this.visit = visit;
        this.interfaceName = interfaceName;
        this.module = module;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", entry=").append(entry);
        sb.append(", visit=").append(visit);
        sb.append(", queryTime=").append(queryTime);
        sb.append(", interfaceName=").append(interfaceName);
        sb.append(", module=").append(module);
        sb.append("]");
        return sb.toString();
    }
}
