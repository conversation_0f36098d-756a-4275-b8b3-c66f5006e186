package com.allcore.platform.vo;

import com.allcore.platform.entity.Sample;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 平台模板表视图实体类
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SampleVO extends Sample {
	private static final long serialVersionUID = 1L;

	/**
	 * 平台名称
	 */
	@ApiModelProperty(value = "平台名称")
	private String platformName;

}
