package com.allcore.platform.dto;

import lombok.Data;

/**
 * （巡检结果zt）
 * <AUTHOR>
 * @date 2022/10/12 14:47
 */
@Data
public class WorkOrderResultZtDTO {

	/**
	 * 巡检guid
	 */
    private String inspectGuid;

	/**
	 * 巡检杆塔数
	 */
    private Integer towerCount;

	/**
	 * 线路guid
	 */
    private String lineGuid;

	/**
	 * 站线名称
	 */
	private String lineName;

	/**
	 * pms分段线路id
	 */
	private String lineReserve2;

	/**
	 * 电压等级
	 */
	private String lineVoltageLevel;

	/**
	 * 飞行公里数
	 */
    private Double kilometers;

	/**
	 * 工作负责人GUID
	 */
    private String workDirectorGuid;

    private String towerGuidList;


}
