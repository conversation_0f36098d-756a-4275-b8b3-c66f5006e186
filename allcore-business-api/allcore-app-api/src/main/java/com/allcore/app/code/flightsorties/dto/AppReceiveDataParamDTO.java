package com.allcore.app.code.flightsorties.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.NullSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: wp
 * @date: 2022/3/14
 */
@Data
@ApiModel(value = "移动端在飞数据回传接口入参实体类")
public class AppReceiveDataParamDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 工单guid(非必传)
     */
    @ApiModelProperty(value = "工单guid(非必传)")
    private String inspecGuid;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;

    /**
     * 速度 km/h
     */
    @ApiModelProperty(value = "速度")
    private String speed;

    /**
     * 海拔高度 m
     */
    @ApiModelProperty(value = "海拔高度")
    private String altitude;

    /**
     *  监控模块编号
     */
    @ApiModelProperty(value = "监控模块编号")
    private String equipmentNo;

    /**
     * 飞行状态（0 没飞、1 在飞、 2 飞完 3.飞航线  4.下载图 5.传图）
     */
    @ApiModelProperty(value = "飞行状态")
    private String flyStatus;

    /**
     * 当前架次累计飞行时长（单位:秒）
     */
    @ApiModelProperty(value = "当前架次累计飞行时长")
    private String time;

    /**
     * 架次飞行距离 (单位:米)
     */
    @ApiModelProperty(value = "架次飞行距离")
    private String distance;

    /**
     * 架次唯一ID
     */
    @ApiModelProperty(value = "架次唯一ID")
    private String recGuid;

    /**
     * 飞机唯一编码
     */
    @ApiModelProperty(value = "飞机唯一编码")
    private String appCode;

    /**
     * 开始时间(yyyy-MM-dd HH:mm:ss)
     */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /**
     *  结束时间(yyyy-MM-dd HH:mm:ss  非必传)
     */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    /**
     * 用户guid
     */
    @ApiModelProperty(value = "用户guid")
    private String userGuid;

    /**
     * 俯仰角(非必传)
     */
    @ApiModelProperty(value = "俯仰角")
    private String pitchAngle;

    /**
     * 横滚角(非必传)
     */
    @ApiModelProperty(value = "横滚角")
    private String rollAngle;

    /**
     * 航向角(非必传)
     */
    @ApiModelProperty(value = "航向角")
    private String yawAngle;

    /**
     * 遥控信号(非必传)
     */
    @ApiModelProperty(value = "遥控信号")
    private String controlSignal;

    /**
     * 图传信号(非必传)
     */
    @ApiModelProperty(value = "图传信号")
    private String pictureSignal;

    /**
     * 遥控电量(非必传)
     */
    @ApiModelProperty(value = "遥控电量")
    private String controlPower;

    /**
     * 飞机电量(非必传)
     */
    @ApiModelProperty(value = "飞机电量")
    private String planePower;

    /**
     * 电池温度(非必传)
     */
    @ApiModelProperty(value = "电池温度")
    private String batteryTemperature;

    /**
     * 电压(非必传)
     */
    @ApiModelProperty(value = "电压")
    private String voltage;

    /**
     * 电流(非必传)
     */
    @ApiModelProperty(value = "电流")
    private String current;

    /**
     * 卫星数量(非必传)
     */
    @ApiModelProperty(value = "卫星数量")
    @JsonSerialize(nullsUsing = NullSerializer.class)
    private Integer gps;

    /**
     *  HDOP(非必传)
     */
    @ApiModelProperty(value = "HDOP")
    private String hdop;

    /**
     * 组织机构id(首页用到，郁app无关)
     */
    @ApiModelProperty(value = "组织机构id")
    private String deptId;

    @ApiModelProperty(value = "飞手名")
    private String flyerName;

    @ApiModelProperty(value = "无人机名")
    private String uavName;

    @ApiModelProperty(value = "无人机类别")
    private String uavType;

    @ApiModelProperty(value = "无人机型号")
    private String uavModel;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "告警信息")
    private String warning;


}
