package com.allcore.app.code.flightsorties.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-11-26
 */
@Data
public class AppStatisticVO implements Serializable {
	private static final long serialVersionUID = 1L;




	@ApiModelProperty(value = "今日发电")
	private String daySum;

	@ApiModelProperty(value = "全部发电量")
	private String totalSum;

	@ApiModelProperty(value = "装机容量")
	private String capacity;


}
