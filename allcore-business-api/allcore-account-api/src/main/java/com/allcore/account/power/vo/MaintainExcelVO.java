package com.allcore.account.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "MaintainPageVO", description = "维保分页VO")
public class MaintainExcelVO {
	@ApiModelProperty(value = "序号")
	private String order;
	@ApiModelProperty(value = "单位")
	private String unit;
	@ApiModelProperty(value = "中心")
	private String center;
	@ApiModelProperty(value = "班组")
	private String team;
	@ApiModelProperty(value = "型号")
	private String productModel;
	@ApiModelProperty(value = "产品序列号")
	private String productSN;
	@ApiModelProperty(value = "服务类型")
	private String serviceType;
	@ApiModelProperty(value = "申请时间")
	private String applyTime;
	@ApiModelProperty(value = "维保状态")
	private String maintainStatus;
	@ApiModelProperty(value = "有无保险")
	private String hasInsurance;

	private String productGuid;
}
