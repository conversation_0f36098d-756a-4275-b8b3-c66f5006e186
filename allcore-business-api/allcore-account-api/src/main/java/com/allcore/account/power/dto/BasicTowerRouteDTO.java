package com.allcore.account.power.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2023/2/28 17:16
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class BasicTowerRouteDTO {
	/**
	 * 专业类型（tms:输电；dms:配电；sms：变电）
	 */
	private String professionalType;
	/**
	 * 杆塔guid
	 */
	private String towerGuid;
	/**
	 * 电压等级
	 */
	private String voltageLevel;
	/**
	 *线路名称
	 */
	private String lineName;

	/**
	 * 杆塔名称
	 */
	private String towerName;
	/**
	 * 航迹名称
	 */
	private String routeName;
	/**
	 * 维度
	 */
	private String realLatitude;
	/**
	 * 经度
	 */
	private String realLongitude;

	private String routeInfoGuid;
}
