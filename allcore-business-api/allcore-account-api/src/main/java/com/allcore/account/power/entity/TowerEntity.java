package com.allcore.account.power.entity;


import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 设备树表
 *
 * <AUTHOR>
 * @date 2022/03/30 13:10
 **/
@Data
@TableName("account_tower_ledger")
public class TowerEntity extends ZxhcEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "杆塔guid")
	private String towerGuid;

	@ApiModelProperty(value = "所属线路guid")
	private String towerLineGuid;

	@ApiModelProperty(value = "杆塔名称")
	private String name;

	@ApiModelProperty(value = "坐标")
	private String geoPosition;

	@ApiModelProperty(value = "所属地市")
	private String city;

	@ApiModelProperty(value = "所属线路psrId")
	private String line;

	@ApiModelProperty(value = "运维单位")
	private String maintOrg;

	@ApiModelProperty(value = "设备主人")
	private String equipmentOwner;

	@ApiModelProperty(value = "电压等级")
	private String voltageLevel;

	@ApiModelProperty(value = "运行状态")
	private String psrState;

	@ApiModelProperty(value = "投运日期")
	private Date startTime;

	@ApiModelProperty(value = "退运日期")
	private Date stopTime;

	@ApiModelProperty(value = "是否农网")
	private String isRural;

	@ApiModelProperty(value = "所属主干/分支线")
	private String branchFeeder;

	@ApiModelProperty(value = "关联物理杆类型")
	private String physicalpoleType;

	@ApiModelProperty(value = "所属开关段")
	private String switchSegment;

	@ApiModelProperty(value = "所属导线")
	private String wire;

	@ApiModelProperty(value = "是否换相")
	private String isChangephase;

	@ApiModelProperty(value = "编号")
	private String poleNum;

	@ApiModelProperty(value = "称呼高")
	private Double norminalHeight;

	@ApiModelProperty(value = "上级线路")
	private String superiorLine;

	@ApiModelProperty(value = "是否分支")
	private String isBranch;

//	@TableField(typeHandler = GeoPointTypeHandler.class)
//	private GeoPoint coordinate;

	@ApiModelProperty(value = "杆塔坐标经度")
	private String towerLongitude;

	@ApiModelProperty(value = "杆塔坐标纬度")
	private String towerLatitude;

	@ApiModelProperty(value = "杆塔排序号")
	private Integer towerSort;

	@ApiModelProperty(value = "详细地址")
	private String location;

	@ApiModelProperty(value = "杆塔性质")
	private String poleNature;

	@ApiModelProperty(value = "设备型号")
	private String model;

	@ApiModelProperty(value = "杆塔材质")
	private String poleMaterial;

	@ApiModelProperty(value = "杆塔回路数")
	private String loopNumber;

	@ApiModelProperty(value = "档距")
	private Double span;

	@ApiModelProperty(value = "代表档距")
	private Double regularDistance;

	@ApiModelProperty(value = "耐张长度")
	private Double strainLength;

	@ApiModelProperty(value = "是否终端")
	private String isTerminal;

	@ApiModelProperty(value = "塔头高度")
	private Double headHeight;

	@ApiModelProperty(value = "海拔高度")
	private Double altitude;

	@ApiModelProperty(value = "杆塔高度")
	private Double poleHigh;

	@ApiModelProperty(value = "是否转角")
	private String isTurn;

	@ApiModelProperty(value = "转角方向")
	private String turnDirection;

	@ApiModelProperty(value = "转角度数")
	private String turnDegrees;

	@ApiModelProperty(value = "是否同杆架设")
	private String isSamePole;

	@ApiModelProperty(value = "地区特征")
	private String regionalism;

	@ApiModelProperty(value = "地质")
	private String geology;

	@ApiModelProperty(value = "地形杆塔地理环境")
	private String terrain;

	@ApiModelProperty(value = "工区编码")
	private String workAreaGuid;

	@ApiModelProperty(value = "维护班组")
	private String maintGroup;

	@ApiModelProperty(value = "资产设备编码")
	private String assetEquipmentCode;

	@ApiModelProperty(value = "资源设备编码")
	private String resourceEquipmentCode;

	@ApiModelProperty(value = "是否在适航区")
	private String isAirworthyArea;

	@ApiModelProperty(value = "适航区来源")
	private String airworthyAreaFrom;

	@ApiModelProperty(value = "禁飞区表Guid")
	private String flyBanGuid;

	@ApiModelProperty(value = "真实坐标经度")
	private String realLongitude;

	@ApiModelProperty(value = "真实坐标纬度")
	private String realLatitude;

	@ApiModelProperty(value = "是否主杆塔")
	private String isMainTower;

	@ApiModelProperty(value = "杆塔分支名称")
	private String towerBranchName;

	@ApiModelProperty(value = "资源id")
	private String psrId;

	@ApiModelProperty(value = "资产id")
	private String astId;

	@ApiModelProperty(value = "PM编码")
	private String pmCode;

	@ApiModelProperty(value = "同杆线路位置")
	private String linePosition;

	@ApiModelProperty(value = "导线排列方式")
	private String wireArrangement;

	@ApiModelProperty(value = "相序/级别")
	private String phasePole;

	@ApiModelProperty(value = "代维单位")
	private String waitMaintainCompany;

	@ApiModelProperty(value = "专业类型")
	private String professionalType;

	@ApiModelProperty(value = "是否老数据")
	private Integer isOld;

	@ApiModelProperty(value = "所属线路")
	@TableField(exist = false)
	private LineEntity belongLine;

	@ApiModelProperty(value = "真实海拔高度")
	private Double realAltitude;

	@ApiModelProperty(value = "偏移坐标经度")
	private String offsetLongitude;

	@ApiModelProperty(value = "偏移坐标纬度")
	private String offsetLatitude;

	@ApiModelProperty(value = "偏移状态 1，0")
	private int offsetStatus;

}
