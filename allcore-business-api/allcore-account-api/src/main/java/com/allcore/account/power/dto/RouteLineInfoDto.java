package com.allcore.account.power.dto;

import com.allcore.common.base.ZxhcEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 巡检航线库(RouteLineInfo)实体类
 *
 * <AUTHOR>
 * @since 2022-08-29 09:45:18
 */
@Data
public class RouteLineInfoDto extends ZxhcEntity implements Serializable {
	private static final long serialVersionUID = -51414644534514944L;
	/**
	 * 航线GUID
	 */
	private String routeInfoGuid;

	/**
	 * 航点list
	 */
	private List<RoutePointInfoDto> routePointInfo;

}

