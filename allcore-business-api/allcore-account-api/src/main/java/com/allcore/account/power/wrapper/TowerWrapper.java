package com.allcore.account.power.wrapper;

import com.allcore.account.power.entity.TowerEntity;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/9 18:20
 */
@Data
public class TowerWrapper extends TowerEntity {
	// 模糊查询
	/**
     * 名称模糊查询
	 */
	private String nameLike;

	/**
	 * 单位模糊查询
	 */
	private String deptCodeLike;

	// 批量查询
	/**
     * 批量查询guid
	 */
	private List<String> towerGuids;
	private List<String> towerLineGuids;
	private List<String> astIds;
	private List<String> airAreaTypes;
	private List<String> voltageLevels;



}
