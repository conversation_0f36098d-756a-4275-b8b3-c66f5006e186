package com.allcore.account.power.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public enum CreateTypeEnum {
    //地面站
    CreateType_DM("demonstration_route", "示教航线"),
    CreateType_JG("laser_point_cloud", "激光点云"),
    CreateType_ZB("coordinate_transformation", "坐标转换");

    private String type;
    private String name;

    CreateTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getNameByType(String type) {
        String name = null;
        CreateTypeEnum[] types = CreateTypeEnum.values();
        for (CreateTypeEnum createTypeEnum : types) {
            if (createTypeEnum.type.equals(type)) {
                name = createTypeEnum.name;
                break;
            }
        }
        return name;
    }

    public static List<Map<String, String>> getEnumList() {
        List<Map<String, String>> list = new ArrayList<>();

        CreateTypeEnum[] types = CreateTypeEnum.values();
        for (CreateTypeEnum createTypeEnum : types) {
            Map<String, String> map = new HashMap<>();
            map.put("code", createTypeEnum.type.toString());
            map.put("name", createTypeEnum.name);
            list.add(map);
        }

        return list;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
