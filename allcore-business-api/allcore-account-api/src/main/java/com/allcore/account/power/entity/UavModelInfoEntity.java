package com.allcore.account.power.entity;


import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
* @author: ldh
* @date: 2023/3/29 17:06
* @description: 无人机型号信息
*/
@Data
@TableName("account_uav_model_info")
@ApiModel("无人机型号信息")
public class UavModelInfoEntity extends ZxhcEntity {
	private static final long serialVersionUID = 1L;

	/**
	 *
	 */
	@ApiModelProperty("模型guid")
	private String modelGuid;
	/**
	 * 无人机品牌
	 */
	@NotBlank(message = "无人机品牌不能为空")
	@ApiModelProperty("无人机品牌")
	private String uavBrand;
	/**
	 * 型号
	 */
	@NotBlank(message = "型号不能为空")
	@ApiModelProperty("无人机型号")
	private String modelName;
	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty("大类别")
	private String bigUavType;

	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty("中类别")
	private String middleUavType;

	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty("小类别")
	private String smallUavType;

	/**
	 * 是否有RTK功能
	 */
	@NotNull(message = "是否有RTK功能不能为空")
	@ApiModelProperty("是否有RTK功能 ")
	private String haveRtk;
	/**
	 * 空机重量(千克)
	 */
	@NotNull(message = "空机重量不能为空")
	@ApiModelProperty("空机重量(千克)")
	private BigDecimal emptyWeight;
	/**
	 * 最大起飞重量(千克)
	 */
	@NotNull(message = "最大起飞重量不能为空")
	@ApiModelProperty("最大起飞重量(千克)")
	private BigDecimal maxTakeoffWeight;
	/**
	 * 最大抗风等级
	 */
	@NotNull(message = "抗风等级不能为空")
	@ApiModelProperty("最大抗风等级")
	private Integer resistingWind;
	/**
	 * 最大速度(km/h)
	 */
	@NotNull(message = "最大速度不能为空")
	@ApiModelProperty("最大速度(km/h)")
	private BigDecimal maxSpeed;
	/**
	 * 最大飞行高度(米)
	 */
	@NotNull(message = "最大飞行高度不能为空")
	@ApiModelProperty("最大飞行高度(米)")
	private BigDecimal maxFlightAltitude;
	/**
	 * 续航时间（分钟）
	 */
	@NotNull(message = "续航时间不能为空")
	@ApiModelProperty("续航时间（分钟）")
	private BigDecimal enduranceTime;
	/**
	 * 机身长度(毫米)
	 */
	@NotNull(message = "机身长度不能为空")
	@ApiModelProperty("机身长度(毫米)")
	private BigDecimal fuselageLength;
	/**
	 * 机身高度(毫米)
	 */
	@NotNull(message = "机身高度不能为空")
	@ApiModelProperty("机身高度(毫米)")
	private BigDecimal fuselageHeight;
	/**
	 * 机身宽度(毫米)
	 */
	@NotNull(message = "机身宽度不能为空")
	@ApiModelProperty("机身宽度(毫米)")
	private BigDecimal fuselageWidth;

	/**
	 * 作业半径( 米)
	 */
	@ApiModelProperty("作业半径( 米)")
	private BigDecimal operatingRadius;
	/**
	 * 起降方式
	 */
	@ApiModelProperty("起降方式")
	private String takeoffAndLandingMode;
	/**
	 * 相机传感器类型(数据字典)
	 */
	@ApiModelProperty("相机传感器类型(数据字典)")
	private String cameraSensorType;

	/**
	 * 相机型号
	 */
	@ApiModelProperty("相机型号")
	private String cameraModel;
	/**
	 * 镜头
	 */
	@ApiModelProperty("镜头")
	private String scene;
	/**
	 * 像素
	 */
	@ApiModelProperty("像素")
	private String pixel;
	/**
	 * ISO范围
	 */
	@ApiModelProperty("ISO范围")
	private String iosRange;
	/**
	 * 云台
	 */
	@ApiModelProperty("云台")
	private String yunTai;

	@TableField(exist = false)
	private List<String> fileGuids;

}
