package com.allcore.account.power.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: ldh
 * @date: 2022/9/14 19:11
 * @description: 无人机备件分页查询条件
 */
@Data
@ApiModel("无人机备件分页查询条件")
public class UavSparePartPageDTO {

	@ApiModelProperty("所属单位")
	private String deptCode;

	/**
	 * 备件名称
	 */
	@ApiModelProperty("备件名称")
	private String sparePartName;

	/**
	 * 备件品牌
	 */
	@ApiModelProperty("备件品牌")
	private String sparePartBrand;

	/**
	 * 备品备件类型
	 */
	@ApiModelProperty("部件品牌")
	private String uavBrand;

	/**
	 * 部件型号
	 */
	@ApiModelProperty("部件型号")
	private String modelGuid;
}
