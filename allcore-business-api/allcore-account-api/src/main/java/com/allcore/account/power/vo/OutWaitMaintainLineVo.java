package com.allcore.account.power.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2022/10/26 0026 11:21
 */
@Data
public class OutWaitMaintainLineVo {

	@ApiModelProperty("序号")
	private Integer serialNumber;

	@ApiModelProperty("电压等级")
	private String lineVoltageLevel;

	@ApiModelProperty("线路名称")
	private String lineName;

	@ApiModelProperty("线路guid")
	private String lineGuid;

	@ApiModelProperty("运维中心")
	private String workAreaName;

	@ApiModelProperty("运维班组")
	private String maintGroup;

	@ApiModelProperty("运维单位")
	private String deptName;

	@ApiModelProperty("总长")
	private String lineLength;

	@ApiModelProperty("杆塔数量")
	private Integer towerNumber;
}
