package com.allcore.account.power.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "MaintainComponentDTO", description = "维保部件DTO")
public class LossDetermineDTO {

	@ApiModelProperty(value = "维保GUID")
	private String maintainGuid;

	@ApiModelProperty(value = "定损情况")
	private String lossDetermineSituation;

	@ApiModelProperty(value = "处理方式")
	private String solution;

	@ApiModelProperty(value = "维修意见")
	private String repairAdvice;

	@ApiModelProperty(value = "维修价格")
	private Double repairPrice;

	@ApiModelProperty(value = "是否提交")
	private String submit;

}
