package com.allcore.account.power.feign;

import com.allcore.account.power.dto.FlyOperatorPageListQueryDTO;
import com.allcore.account.power.entity.FlyOperatorInfoEntity;
import com.allcore.account.power.vo.FlyOperatorDetailVOEntity;
import com.allcore.common.base.OverallViewEnity;
import com.allcore.core.launch.constant.AppConstant;
import com.allcore.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 飞手人员管理feign接口
 *
 * <AUTHOR>
 * @Date 2022/05/06 16:21
 **/
@FeignClient(
	value = AppConstant.APPLICATION_ACCOUNT_NAME,
	fallback = FlyOperatorClientBack.class
)
public interface IFlyOperatorClient {
	String API_PREFIX = "/client/flyOperator";
	String GET_FLY_OPERATOR_ALL = API_PREFIX + "/getFlyOperatorAll";

	String SAVE_OPERATOR_FROM_USER = API_PREFIX + "/saveFromUser";

	/**
	 * 提供给全景平台
	 */
	String PANORAMA_LIST = API_PREFIX + "/panorama/list";

	/**
	 * 查询所有飞手人员信息
	 *
	 * @param flyOperatorDTO
	 * @return
	 */
	@PostMapping(GET_FLY_OPERATOR_ALL)
	R<List<FlyOperatorDetailVOEntity>> getFlyOperatorAll(@RequestBody FlyOperatorPageListQueryDTO flyOperatorDTO);

	/**
	 * 从ISC用户新增的人员飞手信息
	 *
	 * @param flyOperatorInfoEntity
	 * @return
	 */
	@PostMapping(SAVE_OPERATOR_FROM_USER)
	R<FlyOperatorInfoEntity> saveFromUser(@RequestBody FlyOperatorInfoEntity flyOperatorInfoEntity);

	@PostMapping(PANORAMA_LIST)
	R<OverallViewEnity> panoramaList(@RequestParam(required = false) int page, @RequestParam(required = false) int limit, @RequestParam(required = false) String unitGUID, @RequestParam(required = false) String opePersonnelSex, @RequestParam(required = false) String professionalType);
}
