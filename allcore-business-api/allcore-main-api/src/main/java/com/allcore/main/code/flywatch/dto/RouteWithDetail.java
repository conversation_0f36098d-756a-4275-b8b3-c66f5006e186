package com.allcore.main.code.flywatch.dto;

import com.allcore.main.code.flywatch.entity.Route;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 航线库（文件+详情）
 */
@Data
public class RouteWithDetail extends Route {


    /**
     * 拆分的航迹文件guid
     */
    @ApiModelProperty(value = "拆分的航迹文件guid")
    private String subFileGuid;
    /**
     * 设备类型 线路用line_前缀类型
     */
    @ApiModelProperty(value = "设备类型 线路用line_前缀类型")
    private String deviceType;
    /**
     * 设备GUID
     */
    @ApiModelProperty(value = "设备GUID")
    private String deviceId;
    /**
     * 设备父id
     */
    @ApiModelProperty(value = "设备父id")
    private String parentDeviceId;

}
