package com.allcore.main.code.system.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 策略表
 *
 * <AUTHOR>
 * @date 2022/04/28 09:17
 **/
@Data
@TableName("main_strategy")
@EqualsAndHashCode(callSuper = true)
public class Strategy extends ZxhcEntity implements Serializable {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "策略全称")
	private String strategyName;

	@ApiModelProperty(value = "使用次数")
	private int usageTimes;

	@ApiModelProperty(value = "是否启用")
	private String isEnable;
}
