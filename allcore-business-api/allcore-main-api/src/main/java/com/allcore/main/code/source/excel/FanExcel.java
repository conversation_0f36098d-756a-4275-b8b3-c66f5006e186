package com.allcore.main.code.source.excel;

import java.util.Date;

import javax.validation.constraints.*;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.allcore.main.code.common.dto.BaseExcelDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 风机Excel
 *
 * @author: taoye
 * @since 2023-10-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FanExcel extends BaseExcelDTO {

    @Excel(name = "*设备名称")
    @NotBlank(message = "不能为空")
    @Size(min = 1, max = 30, message = "长度范围 1-30")
    private String deviceName;

    @Excel(name = "*风机号")
    @NotBlank(message = "不能为空")
    @Size(min = 2, max = 30, message = "长度范围 2-30")
    private String fanNum;

    @Excel(name = "经度")
    @DecimalMin(value = "-180", message = "最小值 -180")
    @DecimalMax(value = "180", message = "最大值 180")
    private String longitude;

    @Excel(name = "纬度")
    @DecimalMin(value = "-90", message = "最小值为 -90")
    @DecimalMax(value = "90", message = "最大值90")
    private String latitude;

    @Excel(name = "高程")
    private String elevation;
    @Excel(name = "塔筒高度")
    private String towerHeight;
    @Excel(name = "叶片预弯")
    private String bendingBlades;

    @Excel(name = "*风机型号")
    @NotBlank(message = "不能为空")
    @Size(min = 2, max = 20, message = "长度范围 2-20")
    private String model;

    @Excel(name = "*风机生产厂家")
    @NotBlank(message = "不能为空")
    @Size(min = 2, max = 30, message = "长度范围 2-30")
    private String manufacturer;

    @Excel(name = "*容量/MW")
    @NotNull(message = "不能为空")
    @Digits(integer = 8, fraction = 2, message = "整数限制8位,小数限制2位")
    private String capacity;

    @Excel(name = "*风机高度(M)")
    @NotNull(message = "不能为空")
    @Digits(integer = 8, fraction = 2, message = "整数限制8位,小数限制2位")
    private String height;

    @Excel(name = "*叶片长度(M)")
    @NotNull(message = "不能为空")
    @Digits(integer = 8, fraction = 2, message = "整数限制8位,小数限制2位")
    private String bladeLength;

    @Excel(name = "*叶片生产厂家")
    @NotBlank(message = "不能为空")
    @Size(min = 2, max = 30, message = "长度范围 2-30")
    private String bladeManufacturer;

    @Excel(name = "*省份")
    @NotBlank(message = "不能为空")
    private String province;

    @Excel(name = "*投运时间", format = "yyyy-MM-dd")
    @NotNull(message = "不能为空")
    private Date putDate;

    private String deptCode;

}
