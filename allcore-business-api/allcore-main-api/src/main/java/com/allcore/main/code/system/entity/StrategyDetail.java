package com.allcore.main.code.system.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 策略明细表
 *
 * <AUTHOR>
 * @date 2022/04/28 09:19
 **/
@Data
@TableName("main_strategy_detail")
@EqualsAndHashCode(callSuper = true)
public class StrategyDetail extends ZxhcEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "策略id")
	private String strategyId;

	@ApiModelProperty(value = "厂家算法id")
	private String algorithmManufacturersId;

	@ApiModelProperty(value = "模型")
	private String strategyDetailModel;

	@ApiModelProperty(value = "是否启用")
	private String isEnable;
}
