package com.allcore.main.code.flywatch.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 空域生成word中无人机信息vo
 *
 * <AUTHOR>
 */
@Data
public class ExportWordPlaneVo {

	/**
	 * 无人机品牌
	 */
	private String uavBrand;
	/**
	 * 型号
	 */
	private String modelName;
	/**
	 * 类别
	 */
	private String uavType;
	/**
	 * 续航时间（分钟）
	 */
	private BigDecimal enduranceTime;
	/**
	 * 最大抗风等级
	 */
	private Integer resistingWind;
	/**
	 * 最大速度(km/h)
	 */
	private BigDecimal maxSpeed;
	/**
	 * 机身长度(毫米)
	 */
	private BigDecimal fuselageLength;
	/**
	 * 机身高度(毫米)
	 */
	private BigDecimal fuselageHeight;
	/**
	 * 机身宽度(毫米)
	 */
	private BigDecimal fuselageWidth;
	/**
	 * 机型图片路径
	 */
	private String fileGuids;
	/**
	 * 起降方式
	 */
	private String takeoffAndLandingMode;
	/**
	 * 控制方式
	 */
	private String controlMode;
	/**
	 * 作业半径( 米)
	 */
	private BigDecimal operatingRadius;

	/**
	 * 最大飞行高度（米）
	 */
	private BigDecimal maxFlightAltitude;

}
