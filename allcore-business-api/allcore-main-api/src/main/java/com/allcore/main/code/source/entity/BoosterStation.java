package com.allcore.main.code.source.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
@TableName("main_booster_station")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BoosterStation对象", description = "BoosterStation对象")
public class BoosterStation extends ZxhcEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 升压站guid
     */
    @ApiModelProperty(value = "升压站guid")
    private String boosterStationGuid;
    /**
     * 升压站名称
     */
    @ApiModelProperty(value = "升压站名称")
    private String boosterStationName;
    /**
     * 电压等级
     */
    @ApiModelProperty(value = "电压等级")
    private String voltageLevel;
    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String longitude;
    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String latitude;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private String unitPersonName;
    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String tel;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;


}
