package com.allcore.main.code.flywatch.entity;

import com.allcore.core.mp.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@TableName("main_airspace_file")
@ApiModel(value = "AirspaceFile对象", description = "AirspaceFile对象")
public class AirspaceFile {

	@ApiModelProperty("主键id")
	@TableId(
			value = "id",
			type = IdType.ASSIGN_UUID
	)
	private String id;

    /**
     * 空域申请ID
     */
    @ApiModelProperty(value = "空域申请ID")
    private String airspaceId;

    /**
     * 文件类型 1空域截图 2电话录音
     */
    @ApiModelProperty(value = "文件类型 1空域截图 2电话录音")
    private String fileType;
    /**
     * 文件GUID
     */
    @ApiModelProperty(value = "文件GUID")
    private String fileGuid;


}
