package com.allcore.main.code.source.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.allcore.main.code.common.dto.BaseExcelDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * @program: bl
 * @description: 逆变器Excel类
 * @author: fanxiang
 * @create: 2025-07-10 09:15
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class PvInverterExcel extends BaseExcelDTO {


    @Excel(name="*逆变器编号")
    @NotBlank(message = "不能为空")
    @Size(min=1,max=30,message = "逆变器编号长度范围 1-30")
    private String inverterNumber;

    @Excel(name="*设备名称")
    @NotBlank(message = "不能为空")
    @Size(min=1,max=30,message = "设备名称长度范围 1-30")
    private String deviceName;

    @Excel(name="*逆变器序列号")
    @NotBlank(message = "不能为空")
    @Size(min=1,max=30,message = "逆变器序列号长度范围 1-30")
    private String inverterSerialNumber;

    @Excel(name="逆变器型号")
    @Size(min=1,max=30,message = "逆变器型号长度范围 1-30")
    private String inverterModel;

    @Excel(name = "经度")
    @DecimalMin(value = "-180", message = "最小值 -180")
    @DecimalMax(value = "180", message = "最大值 180")
    private String longitude;

    @Excel(name = "纬度")
    @DecimalMin(value = "-90", message = "最小值为 -90")
    @DecimalMax(value = "90", message = "最大值90")
    private String latitude;

    @Excel(name = "高程")
    private String height;

    @Excel(name ="*关联组串")
    private String attachedString;

    @Excel(name = "生产厂家")
    private String manufacturer;

    @Excel(name = "额定功率(MW)")
    private String ratedPower;

    @Excel(name ="*关联光伏子阵")
    private String attachedPvArea;

    @Excel(name = "投入使用日期",format = "yyyy-MM-dd")
    private Date usageTime;

    @ApiModelProperty(value = "单位code")
    private String deptCode;

}
