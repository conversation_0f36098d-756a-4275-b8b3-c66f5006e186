package com.allcore.main.code.common.feign;

import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.common.dto.DeviceCommonDTO;
import com.allcore.main.code.common.vo.BasicCommonVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 28 7月 2025
 */
@FeignClient(name = LauncherConstant.MAIN_SERVER_NAME)
public interface ICommonClient {

    @PostMapping("/main/common/getDeviceList")
    R<List<BasicCommonVO>> getCommonDeviceList(@RequestBody DeviceCommonDTO dto);
}
